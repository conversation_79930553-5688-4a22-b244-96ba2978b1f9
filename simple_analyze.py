import os
import sys
import pandas as pd

def analyze_results(csv_file, txt_file):
    """简单分析训练结果并生成报告"""
    print(f"分析 {csv_file} 并生成报告到 {txt_file}...")
    
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file)
        
        # 获取关键指标
        final_epoch = df.iloc[-1]
        best_map = df['metrics/mAP50(B)'].max()
        best_map_epoch = df['metrics/mAP50(B)'].idxmax() + 1
        
        # 写入报告
        with open(txt_file, 'w') as f:
            f.write("# YOLOv8s 光伏异常检测模型训练结果\n\n")
            
            f.write("## 训练摘要\n")
            f.write(f"总训练轮次: {len(df)}\n")
            f.write(f"最佳mAP50: {best_map:.4f} (第{best_map_epoch}轮)\n\n")
            
            f.write("## 最终模型性能\n")
            f.write(f"轮次: {final_epoch['epoch']}\n")
            f.write(f"mAP50: {final_epoch['metrics/mAP50(B)']:.4f}\n")
            f.write(f"mAP50-95: {final_epoch['metrics/mAP50-95(B)']:.4f}\n")
            f.write(f"精度: {final_epoch['metrics/precision(B)']:.4f}\n")
            f.write(f"召回率: {final_epoch['metrics/recall(B)']:.4f}\n")
        
        print(f"报告已保存至 {txt_file}")
    except Exception as e:
        print(f"分析时出错: {e}")
        return False
    
    return True

if __name__ == "__main__":
    csv_file = "models/trained/pv_anomaly_yolov8s.csv"
    txt_file = "models/trained/pv_anomaly_yolov8s.txt"
    
    if len(sys.argv) > 1:
        csv_file = sys.argv[1]
    if len(sys.argv) > 2:
        txt_file = sys.argv[2]
    
    analyze_results(csv_file, txt_file) 