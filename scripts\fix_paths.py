import os
import json
from pathlib import Path

def fix_ultralytics_settings():
    """修复Ultralytics设置中的数据集路径"""
    # 获取Ultralytics设置文件路径
    settings_dir = os.path.join(os.path.expanduser("~"), "AppData", "Roaming", "Ultralytics")
    settings_file = os.path.join(settings_dir, "settings.json")
    
    # 确保目录存在
    os.makedirs(settings_dir, exist_ok=True)
    
    # 获取当前工作目录
    current_dir = os.getcwd()
    
    # 准备设置内容
    settings = {
        "datasets_dir": current_dir,  # 将数据集目录直接设置为当前项目目录
        "weights_dir": os.path.join(current_dir, "weights"),
        "runs_dir": os.path.join(current_dir, "runs")
    }
    
    # 如果设置文件已存在，加载并更新它
    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r') as f:
                existing_settings = json.load(f)
                
            # 更新现有设置而不是完全覆盖
            existing_settings.update(settings)
            settings = existing_settings
            print(f"已更新现有Ultralytics设置文件: {settings_file}")
        except Exception as e:
            print(f"读取现有设置文件时出错: {e}")
            print(f"将创建新的设置文件")
    
    # 保存设置文件
    try:
        with open(settings_file, 'w') as f:
            json.dump(settings, f, indent=4)
        print(f"已保存Ultralytics设置到: {settings_file}")
        print(f"现在数据集目录设置为: {settings['datasets_dir']}")
        return True
    except Exception as e:
        print(f"保存设置文件时出错: {e}")
        return False

def fix_data_yaml():
    """修复数据集YAML中的路径"""
    data_yaml_path = os.path.join("data", "pv_thermal_detection", "data.yaml")
    if not os.path.exists(data_yaml_path):
        print(f"错误: 未找到数据集配置文件 {data_yaml_path}")
        return False
    
    try:
        # 读取YAML文件
        import yaml
        with open(data_yaml_path, 'r', encoding='utf-8') as f:
            data_config = yaml.safe_load(f)
        
        # 检查并修复路径
        if 'train' in data_config:
            train_path = data_config['train']
            if not os.path.exists(os.path.join("data", "pv_thermal_detection", train_path)):
                # 尝试修复路径
                possible_paths = [
                    os.path.join("train", "images"),
                    "train"
                ]
                for path in possible_paths:
                    if os.path.exists(os.path.join("data", "pv_thermal_detection", path)):
                        data_config['train'] = path
                        print(f"更新训练集路径为: {path}")
                        break
        
        if 'val' in data_config:
            val_path = data_config['val']
            if not os.path.exists(os.path.join("data", "pv_thermal_detection", val_path)):
                # 尝试修复路径
                possible_paths = [
                    os.path.join("valid", "images"),
                    "valid",
                    os.path.join("val", "images"),
                    "val"
                ]
                for path in possible_paths:
                    if os.path.exists(os.path.join("data", "pv_thermal_detection", path)):
                        data_config['val'] = path
                        print(f"更新验证集路径为: {path}")
                        break
        
        if 'test' in data_config:
            test_path = data_config['test']
            if not os.path.exists(os.path.join("data", "pv_thermal_detection", test_path)):
                # 尝试修复路径
                possible_paths = [
                    os.path.join("test", "images"),
                    "test"
                ]
                for path in possible_paths:
                    if os.path.exists(os.path.join("data", "pv_thermal_detection", path)):
                        data_config['test'] = path
                        print(f"更新测试集路径为: {path}")
                        break
        
        # 备份原始文件
        import shutil
        shutil.copy(data_yaml_path, f"{data_yaml_path}.bak")
        print(f"已备份原始配置文件到 {data_yaml_path}.bak")
        
        # 保存修改后的配置
        with open(data_yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(data_config, f, default_flow_style=False)
        
        print(f"已更新数据集配置文件: {data_yaml_path}")
        return True
    except Exception as e:
        print(f"修复数据集配置文件时出错: {e}")
        return False

def scan_dataset_structure():
    """扫描数据集结构并打印出来"""
    print("\n扫描数据集结构...")
    import glob
    
    base_dir = os.path.join("data", "pv_thermal_detection")
    if not os.path.exists(base_dir):
        print(f"错误: 找不到数据集目录 {base_dir}")
        return
    
    # 列出目录结构
    print(f"\n{base_dir} 目录结构:")
    for item in os.listdir(base_dir):
        item_path = os.path.join(base_dir, item)
        if os.path.isdir(item_path):
            print(f"- 目录: {item}")
            for subitem in os.listdir(item_path):
                subitem_path = os.path.join(item_path, subitem)
                if os.path.isdir(subitem_path):
                    file_count = len(os.listdir(subitem_path))
                    print(f"  - 子目录: {subitem} ({file_count} 个文件)")
                else:
                    print(f"  - 文件: {subitem} ({os.path.getsize(subitem_path) / 1024:.1f} KB)")
        else:
            print(f"- 文件: {item} ({os.path.getsize(item_path) / 1024:.1f} KB)")
    
    # 计算图像和标签数量
    train_images = glob.glob(os.path.join(base_dir, "train", "**", "*.jpg"), recursive=True)
    train_images += glob.glob(os.path.join(base_dir, "train", "**", "*.png"), recursive=True)
    valid_images = glob.glob(os.path.join(base_dir, "valid", "**", "*.jpg"), recursive=True)
    valid_images += glob.glob(os.path.join(base_dir, "valid", "**", "*.png"), recursive=True)
    test_images = glob.glob(os.path.join(base_dir, "test", "**", "*.jpg"), recursive=True)
    test_images += glob.glob(os.path.join(base_dir, "test", "**", "*.png"), recursive=True)
    
    train_labels = glob.glob(os.path.join(base_dir, "train", "**", "*.txt"), recursive=True)
    valid_labels = glob.glob(os.path.join(base_dir, "valid", "**", "*.txt"), recursive=True)
    test_labels = glob.glob(os.path.join(base_dir, "test", "**", "*.txt"), recursive=True)
    
    print("\n数据集统计:")
    print(f"- 训练集: {len(train_images)} 张图像, {len(train_labels)} 个标签文件")
    print(f"- 验证集: {len(valid_images)} 张图像, {len(valid_labels)} 个标签文件")
    print(f"- 测试集: {len(test_images)} 张图像, {len(test_labels)} 个标签文件")
    print(f"- 总计: {len(train_images) + len(valid_images) + len(test_images)} 张图像")

def install_dependencies():
    """安装缺少的依赖"""
    print("\n安装缺少的依赖...")
    import subprocess
    
    try:
        subprocess.run(["pip", "install", "scikit-learn", "seaborn"], check=True)
        print("已安装scikit-learn和seaborn")
        return True
    except Exception as e:
        print(f"安装依赖时出错: {e}")
        return False

if __name__ == "__main__":
    print("="*80)
    print("修复Ultralytics训练环境")
    print("="*80)
    
    # 扫描数据集结构
    scan_dataset_structure()
    
    # 修复设置
    fix_ultralytics_settings()
    
    # 修复数据集YAML配置
    fix_data_yaml()
    
    # 安装缺少的依赖
    install_dependencies()
    
    print("\n修复完成！请再次运行 train.bat 开始训练")
    print("="*80) 