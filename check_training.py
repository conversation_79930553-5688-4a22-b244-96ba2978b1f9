import os
import glob
from pathlib import Path
import time

def check_latest_run():
    """检查最新的训练运行并显示进度"""
    print("\n========== 检查训练进度 ==========")
    
    # 检查运行目录
    runs_dir = Path("runs/detect")
    if not os.path.exists(runs_dir):
        print(f"没有找到训练运行目录: {runs_dir}")
        return False
    
    # 查找所有训练目录
    run_dirs = list(runs_dir.glob("*"))
    if not run_dirs:
        print("未找到任何训练运行")
        return False
    
    # 按修改时间排序，找出最近的运行
    run_dirs.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    latest_run = run_dirs[0]
    
    print(f"最新的训练运行: {latest_run}")
    print(f"开始时间: {time.ctime(os.path.getctime(latest_run))}")
    print(f"最后修改时间: {time.ctime(os.path.getmtime(latest_run))}")
    
    # 检查是否有权重文件
    weights_dir = latest_run / "weights"
    if os.path.exists(weights_dir):
        weight_files = list(weights_dir.glob("*.pt"))
        if weight_files:
            print("\n找到的权重文件:")
            for wf in weight_files:
                size_mb = os.path.getsize(wf) / (1024 * 1024)
                print(f"- {wf.name} ({size_mb:.2f} MB)")
    
    # 检查结果文件
    results_file = latest_run / "results.csv"
    if os.path.exists(results_file):
        print("\n训练结果文件存在:")
        print(f"- {results_file} ({os.path.getsize(results_file)} 字节)")
        
        # 读取最新的几行结果
        try:
            with open(results_file, 'r') as f:
                lines = f.readlines()
                if len(lines) > 1:
                    print("\n最新训练指标:")
                    # 打印标题行
                    headers = lines[0].strip().split(',')
                    print(f"{'轮次':<6} {'损失':<10} {'精度':<10} {'召回率':<10} {'mAP50':<10}")
                    print("-" * 50)
                    
                    # 打印最后5个epoch的结果或所有结果（如果少于5个）
                    for line in lines[-min(5, len(lines)-1):]:
                        values = line.strip().split(',')
                        if len(values) >= 5:
                            epoch = values[0]
                            loss = values[3] if len(values) > 3 else "N/A"
                            precision = values[4] if len(values) > 4 else "N/A"
                            recall = values[5] if len(values) > 5 else "N/A"
                            map50 = values[6] if len(values) > 6 else "N/A"
                            print(f"{epoch:<6} {loss:<10} {precision:<10} {recall:<10} {map50:<10}")
        except Exception as e:
            print(f"读取结果文件时出错: {e}")
    
    # 检查训练状态
    last_modified = os.path.getmtime(latest_run)
    time_since_modified = time.time() - last_modified
    
    if time_since_modified < 60:  # 1分钟内有更新
        print("\n训练状态: 正在进行中")
    elif time_since_modified < 300:  # 5分钟内有更新
        print("\n训练状态: 可能正在进行中，但最近5分钟内没有更新")
    else:
        print(f"\n训练状态: 可能已停止，最后更新于 {time_since_modified/60:.1f} 分钟前")
    
    return True

if __name__ == "__main__":
    check_latest_run() 