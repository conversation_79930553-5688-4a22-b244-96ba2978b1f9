"""
直接在控制台显示训练输出的脚本
"""

import subprocess
import sys
import os

def run_training():
    """运行训练并实时显示输出"""
    print("="*80)
    print("启动YOLOv8s模型训练，输出将实时显示在控制台")
    print("="*80)
    
    # 训练命令
    cmd = [
        sys.executable,  # 当前Python解释器
        "scripts/train_model.py",
        "--cfg", "scripts/train_config_s_quick.yaml",  # 使用快速训练配置
        "--device", "0",  # 明确指定GPU
        "--verbose"  # 输出详细信息
    ]
    
    # 使用subprocess.Popen并将输出直接发送到控制台
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
        bufsize=1  # 行缓冲
    )
    
    try:
        # 实时输出训练进程的输出
        print("\n开始训练，输出如下：")
        print("-"*80)
        
        for line in process.stdout:
            print(line.rstrip())
            sys.stdout.flush()  # 确保立即显示
            
            # 检查是否包含特定的关键字来识别进度
            if "Epoch" in line and "/" in line:
                # 这是epoch进度行
                pass
            elif "mAP" in line:
                # 这是验证性能行
                pass
    except KeyboardInterrupt:
        print("\n用户中断训练")
        process.terminate()
        return
    
    # 等待进程完成
    return_code = process.wait()
    
    if return_code == 0:
        print("\n训练成功完成!")
    else:
        print(f"\n训练过程中出错，返回代码：{return_code}")

if __name__ == "__main__":
    run_training() 