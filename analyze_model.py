import os
import sys
import glob
import yaml
import cv2
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from collections import Counter
import argparse
from ultralytics import YOLO

def parse_args():
    parser = argparse.ArgumentParser(description="分析模型和数据集")
    parser.add_argument('--model', type=str, default=None, help='模型路径')
    parser.add_argument('--data', type=str, default='data/pv_thermal_detection/data.yaml', help='数据配置文件')
    parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--show-images', action='store_true', help='显示图像')
    return parser.parse_args()

def load_data_config(data_yaml):
    """加载数据集配置"""
    try:
        with open(data_yaml, 'r', encoding='utf-8') as f:
            data_config = yaml.safe_load(f)
        return data_config
    except Exception as e:
        print(f"加载数据配置出错: {e}")
        return None

def check_image_labels(data_dir, data_config):
    """检查图像和标签"""
    print("\n" + "="*80)
    print("数据集分析")
    print("="*80)
    
    # 获取路径
    data_dir = Path(data_dir).parent
    train_path = data_dir / data_config.get('train', '').replace('/', os.sep)
    val_path = data_dir / data_config.get('val', '').replace('/', os.sep)
    test_path = data_dir / data_config.get('test', '').replace('/', os.sep)
    
    # 检查训练集
    train_images_path = train_path / "images"
    train_labels_path = train_path / "labels"
    
    if train_images_path.exists():
        train_images = list(train_images_path.glob("*.*"))
        train_images = [f for f in train_images if f.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']]
        print(f"训练图像数量: {len(train_images)}")
    else:
        train_images = []
        print(f"训练图像目录不存在: {train_images_path}")
    
    # 检查验证集
    val_images_path = val_path / "images"
    val_labels_path = val_path / "labels"
    
    if val_images_path.exists():
        val_images = list(val_images_path.glob("*.*"))
        val_images = [f for f in val_images if f.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']]
        print(f"验证图像数量: {len(val_images)}")
    else:
        val_images = []
        print(f"验证图像目录不存在: {val_images_path}")
    
    # 检查测试集
    test_images_path = test_path / "images"
    test_labels_path = test_path / "labels"
    
    if test_images_path.exists():
        test_images = list(test_images_path.glob("*.*"))
        test_images = [f for f in test_images if f.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']]
        print(f"测试图像数量: {len(test_images)}")
    else:
        test_images = []
        print(f"测试图像目录不存在: {test_images_path}")
    
    # 分析标签
    class_counts = {split: Counter() for split in ['train', 'val', 'test']}
    empty_labels = {split: 0 for split in ['train', 'val', 'test']}
    
    # 分析训练集标签
    if train_labels_path.exists():
        for img_file in train_images:
            label_file = train_labels_path / f"{img_file.stem}.txt"
            if label_file.exists():
                with open(label_file, 'r') as f:
                    labels = f.readlines()
                if not labels:
                    empty_labels['train'] += 1
                for label in labels:
                    try:
                        class_id = int(label.split()[0])
                        class_counts['train'][class_id] += 1
                    except:
                        pass
    
    # 分析验证集标签
    if val_labels_path.exists():
        for img_file in val_images:
            label_file = val_labels_path / f"{img_file.stem}.txt"
            if label_file.exists():
                with open(label_file, 'r') as f:
                    labels = f.readlines()
                if not labels:
                    empty_labels['val'] += 1
                for label in labels:
                    try:
                        class_id = int(label.split()[0])
                        class_counts['val'][class_id] += 1
                    except:
                        pass
    
    # 分析测试集标签
    if test_labels_path.exists():
        for img_file in test_images:
            label_file = test_labels_path / f"{img_file.stem}.txt"
            if label_file.exists():
                with open(label_file, 'r') as f:
                    labels = f.readlines()
                if not labels:
                    empty_labels['test'] += 1
                for label in labels:
                    try:
                        class_id = int(label.split()[0])
                        class_counts['test'][class_id] += 1
                    except:
                        pass
    
    # 打印结果
    print("\n各数据集标签统计:")
    class_names = data_config.get('names', [])
    for split in ['train', 'val', 'test']:
        print(f"\n{split.capitalize()}集:")
        print(f"  - 空标签图像数量: {empty_labels[split]}")
        total = sum(class_counts[split].values())
        print(f"  - 总标签数量: {total}")
        
        for class_id, count in sorted(class_counts[split].items()):
            name = class_names[class_id] if class_id < len(class_names) else f"未知类别({class_id})"
            print(f"  - {name}: {count} ({count/total*100:.1f}%)")
    
    # 返回数据集统计信息
    return {
        'train_images': train_images,
        'val_images': val_images,
        'test_images': test_images,
        'class_counts': class_counts,
        'empty_labels': empty_labels,
        'class_names': class_names
    }

def find_best_model(model_path=None):
    """查找最佳模型"""
    if model_path and os.path.exists(model_path):
        return model_path
    
    # 按照优先级查找模型
    potential_paths = [
        "models/pv_thermal_detection_best.pt",  # 优化训练复制的模型
        "runs/detect/pv_thermal_detection_improved/weights/best.pt",  # 优化训练的模型
    ]
    
    # 搜索所有可能的模型文件夹
    for model_dir in glob.glob("runs/detect/*/"):
        potential_paths.append(os.path.join(model_dir, "weights/best.pt"))
    
    for path in potential_paths:
        if os.path.exists(path):
            print(f"找到模型: {path}")
            return path
    
    print("未找到任何模型!")
    return None

def evaluate_model(model_path, data_config, conf=0.25, show_images=False):
    """评估模型性能"""
    if not model_path or not os.path.exists(model_path):
        print("模型文件不存在!")
        return
    
    print("\n" + "="*80)
    print(f"模型评估 - {os.path.basename(model_path)}")
    print("="*80)
    
    try:
        # 加载模型
        model = YOLO(model_path)
        
        # 获取验证集路径
        data_dir = Path(os.path.dirname(data_config.get('path', '')))
        val_path = data_dir / data_config.get('val', '')
        if not val_path.exists():
            print(f"验证集路径不存在: {val_path}")
            return
        
        # 运行验证
        print(f"\n在验证集上评估模型...")
        results = model.val(data=os.path.dirname(data_dir / 'data.yaml'), 
                           split='val',
                           conf=conf,
                           verbose=True)
        
        # 提取性能指标
        metrics = {
            'mAP50': results.box.map50,
            'mAP50-95': results.box.map,
            'precision': results.box.mp,
            'recall': results.box.mr
        }
        
        print("\n主要性能指标:")
        for metric, value in metrics.items():
            print(f"- {metric}: {value:.4f}")
        
        # 分析每个类别的性能
        class_names = data_config.get('names', [])
        print("\n各类别性能:")
        try:
            for i, ap in enumerate(results.box.ap_class_index):
                cls_name = class_names[i] if i < len(class_names) else f"类别 {i}"
                print(f"- {cls_name}:")
                print(f"  - AP50: {results.box.ap50[i]:.4f}")
                print(f"  - Precision: {results.box.mps[i]:.4f}")
                print(f"  - Recall: {results.box.mrs[i]:.4f}")
        except:
            print("未能获取每个类别的详细性能")
        
        # 运行预测并显示结果
        if show_images:
            print("\n运行预测示例...")
            test_path = data_dir / data_config.get('test', '')
            if test_path.exists():
                test_images = list((test_path / "images").glob("*.*"))
                test_images = [str(f) for f in test_images if f.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']]
                
                if test_images:
                    # 选择最多5张图片
                    sample_images = test_images[:5] if len(test_images) > 5 else test_images
                    results = model.predict(source=sample_images, conf=conf, save=True)
                    print(f"预测示例已保存到: {model.predictor.save_dir}")
        
        return metrics
    
    except Exception as e:
        print(f"评估模型时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    args = parse_args()
    
    print("\n" + "="*80)
    print("光伏电板热成像故障检测 - 模型分析")
    print("="*80)
    
    # 加载数据配置
    data_config = load_data_config(args.data)
    if not data_config:
        print(f"无法加载数据配置: {args.data}")
        return
    
    print("\n数据集信息:")
    print(f"- 路径: {os.path.dirname(args.data)}")
    print(f"- 类别数量: {data_config.get('nc', '未知')}")
    print(f"- 类别名称: {data_config.get('names', [])}")
    
    # 分析数据集
    data_info = check_image_labels(os.path.dirname(args.data), data_config)
    
    # 查找最佳模型
    model_path = find_best_model(args.model)
    
    # 评估模型
    if model_path:
        metrics = evaluate_model(model_path, data_config, args.conf, args.show_images)
        
        # 提出改进建议
        if metrics:
            print("\n" + "="*80)
            print("改进建议")
            print("="*80)
            
            mAP50 = metrics['mAP50']
            recall = metrics['recall']
            
            if mAP50 < 0.1:
                print("- 模型性能非常差 (mAP50 < 0.1)，建议:")
                print("  * 大幅增加训练轮数 (epochs > 100)")
                print("  * 降低学习率 (lr0 = 0.001)")
                print("  * 检查标签质量和一致性")
            elif mAP50 < 0.3:
                print("- 模型性能较差 (mAP50 < 0.3)，建议:")
                print("  * 增加训练轮数 (epochs = 100-200)")
                print("  * 使用更强大的模型 (如YOLOv8s)")
                print("  * 增加数据增强 (更高的mosaic和mixup值)")
            elif mAP50 < 0.5:
                print("- 模型性能一般 (mAP50 < 0.5)，建议:")
                print("  * 微调学习率和训练参数")
                print("  * 考虑使用迁移学习或更大模型")
            else:
                print("- 模型性能尚可 (mAP50 > 0.5)，建议:")
                print("  * 微调以改进特定类别的检测效果")
            
            if recall < 0.1:
                print("\n- 召回率极低 (< 0.1)，建议:")
                print("  * 减小置信度阈值 (conf = 0.1)")
                print("  * 检查标签是否过大或过小")
                print("  * 尝试不同的IoU阈值 (iou = 0.5)")
            
            # 类别不平衡问题
            class_counts = data_info['class_counts']['train']
            if class_counts:
                max_count = max(class_counts.values())
                min_count = min(class_counts.values())
                
                if min_count < max_count * 0.1:
                    print("\n- 发现严重类别不平衡问题:")
                    print("  * 考虑使用类别权重 (cls_weights 参数)")
                    print("  * 增加少数类的样本或使用数据合成")
    
    print("\n分析完成!")

if __name__ == "__main__":
    main() 