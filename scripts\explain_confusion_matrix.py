#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
解释YOLOv8训练结果中的混淆矩阵
帮助用户理解模型的性能和错误类型
"""

import os
import sys
import argparse
import yaml
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import cv2

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='解释YOLOv8混淆矩阵')
    parser.add_argument('--matrix', type=str, required=True, 
                        help='混淆矩阵图片路径')
    parser.add_argument('--data_config', type=str, default='data/pv_thermal_detection/data.yaml',
                        help='数据配置文件路径')
    parser.add_argument('--output', type=str, default='confusion_matrix_analysis.txt',
                        help='分析结果输出文件')
    return parser.parse_args()

def load_class_names(data_config):
    """从数据配置文件中加载类别名称"""
    if not os.path.exists(data_config):
        print(f"错误: 找不到数据配置文件: {data_config}")
        return None
    
    try:
        with open(data_config, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            return config.get('names', {})
    except Exception as e:
        print(f"读取类别名称时出错: {e}")
        return None

def analyze_confusion_matrix(matrix_path, class_names):
    """分析混淆矩阵图片并提取有用信息"""
    if not os.path.exists(matrix_path):
        print(f"错误: 找不到混淆矩阵图片: {matrix_path}")
        return None
    
    try:
        # 读取图片
        image = cv2.imread(matrix_path)
        if image is None:
            print(f"错误: 无法读取图片: {matrix_path}")
            return None
        
        # 显示图片
        plt.figure(figsize=(10, 8))
        plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        plt.title("混淆矩阵")
        plt.axis('off')
        plt.tight_layout()
        plt.show()
        
        # 分析结果
        analysis = {
            "image_path": matrix_path,
            "class_names": class_names,
            "notes": "混淆矩阵解释：\n"
                    "- 行表示实际类别（真实标签）\n"
                    "- 列表示预测类别\n"
                    "- 对角线上的数字表示正确分类的样本数量\n"
                    "- 非对角线上的数字表示错误分类的样本数量\n\n"
        }
        
        # 附加类别特定的解释
        analysis["notes"] += "类别解释：\n"
        if class_names:
            for idx, name in class_names.items():
                analysis["notes"] += f"- {name}: 光伏板上的{name}故障\n"
        
        # 常见问题分析
        analysis["notes"] += "\n常见问题分析：\n"
        analysis["notes"] += "1. 对角线值较低 - 该类别的检测精度不高，可能需要更多该类型的训练样本\n"
        analysis["notes"] += "2. 行中非对角线值较高 - 某一实际类别经常被错误地识别为其他类别\n"
        analysis["notes"] += "3. 列中非对角线值较高 - 模型倾向于将其他类别错误地识别为该类别\n"
        
        # 改进建议
        analysis["notes"] += "\n改进建议：\n"
        analysis["notes"] += "1. 增加困难类别的训练样本\n"
        analysis["notes"] += "2. 对容易混淆的类别使用数据增强\n"
        analysis["notes"] += "3. 调整模型的学习率或训练更多轮次\n"
        analysis["notes"] += "4. 考虑使用更大或更复杂的模型，如YOLOv8m或YOLOv8l\n"
        
        return analysis
    except Exception as e:
        print(f"分析混淆矩阵时出错: {e}")
        return None

def save_analysis(analysis, output_path):
    """保存分析结果到文件"""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("YOLOv8混淆矩阵分析\n")
            f.write("======================\n\n")
            
            f.write(f"混淆矩阵图片: {analysis['image_path']}\n\n")
            
            if analysis['class_names']:
                f.write("类别名称:\n")
                for idx, name in analysis['class_names'].items():
                    f.write(f"  {idx}: {name}\n")
                f.write("\n")
            
            f.write(analysis['notes'])
            
        print(f"分析结果已保存到: {output_path}")
        return True
    except Exception as e:
        print(f"保存分析结果时出错: {e}")
        return False

def main():
    """主函数"""
    args = parse_args()
    
    # 加载类别名称
    class_names = load_class_names(args.data_config)
    
    # 分析混淆矩阵
    analysis = analyze_confusion_matrix(args.matrix, class_names)
    if analysis:
        # 保存分析结果
        save_analysis(analysis, args.output)
        
        # 打印分析结果
        print("\n" + "="*80)
        print("混淆矩阵分析")
        print("="*80)
        print(analysis['notes'])
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 