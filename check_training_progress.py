import os
import time
import yaml
import glob
from datetime import datetime

def check_progress(training_dir):
    """检查训练目录，分析训练进度"""
    print(f"\n{'='*60}")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查目录是否存在
    if not os.path.exists(training_dir):
        print(f"训练目录 {training_dir} 不存在!")
        return
    
    # 检查weights目录
    weights_dir = os.path.join(training_dir, 'weights')
    if not os.path.exists(weights_dir):
        print(f"权重目录 {weights_dir} 不存在，训练可能尚未开始保存权重")
    else:
        # 列出所有权重文件
        weight_files = glob.glob(os.path.join(weights_dir, '*.pt'))
        if weight_files:
            print(f"找到 {len(weight_files)} 个权重文件:")
            for wf in sorted(weight_files):
                file_size = os.path.getsize(wf) / (1024 * 1024)  # MB
                mod_time = datetime.fromtimestamp(os.path.getmtime(wf)).strftime('%Y-%m-%d %H:%M:%S')
                print(f"  - {os.path.basename(wf)}: {file_size:.2f} MB, 修改时间: {mod_time}")
        else:
            print("未找到权重文件，训练可能尚未完成首个保存周期")
    
    # 检查训练日志
    results_file = os.path.join(training_dir, 'results.csv')
    if os.path.exists(results_file):
        print("\n训练日志存在，读取最新进度:")
        with open(results_file, 'r') as f:
            lines = f.readlines()
            if len(lines) > 1:  # 头部行 + 至少一行数据
                header = lines[0].strip().split(',')
                latest = lines[-1].strip().split(',')
                
                # 创建进度字典
                progress = {h: v for h, v in zip(header, latest)}
                
                # 显示关键指标
                print(f"  当前轮次: {progress.get('epoch', 'N/A')}/{progress.get('epochs', 'N/A')}")
                print(f"  盒子损失: {progress.get('box_loss', 'N/A')}")
                print(f"  类别损失: {progress.get('cls_loss', 'N/A')}")
                print(f"  mAP@50: {progress.get('metrics/mAP50(B)', 'N/A')}")
                print(f"  mAP@50-95: {progress.get('metrics/mAP50-95(B)', 'N/A')}")
                print(f"  精确率: {progress.get('metrics/precision(B)', 'N/A')}")
                print(f"  召回率: {progress.get('metrics/recall(B)', 'N/A')}")
            else:
                print("训练日志文件存在，但尚无训练数据")
    else:
        print("训练日志文件不存在，训练可能尚未到达第一个验证点")
    
    # 检查args文件
    args_file = os.path.join(training_dir, 'args.yaml')
    if os.path.exists(args_file):
        with open(args_file, 'r') as f:
            args = yaml.safe_load(f)
            print("\n训练配置:")
            print(f"  总轮次: {args.get('epochs', 'N/A')}")
            print(f"  批次大小: {args.get('batch', 'N/A')}")
            print(f"  图像尺寸: {args.get('imgsz', 'N/A')}")
            print(f"  设备: {args.get('device', 'N/A')}")
            print(f"  数据集: {args.get('data', 'N/A')}")
    
    # 检查是否有训练预览图
    preview_files = [
        os.path.join(training_dir, 'confusion_matrix.png'),
        os.path.join(training_dir, 'results.png'),
        os.path.join(training_dir, 'val_batch0_pred.jpg')
    ]
    
    existing_previews = [f for f in preview_files if os.path.exists(f)]
    if existing_previews:
        print("\n存在训练预览图:")
        for p in existing_previews:
            mod_time = datetime.fromtimestamp(os.path.getmtime(p)).strftime('%Y-%m-%d %H:%M:%S')
            print(f"  - {os.path.basename(p)}, 修改时间: {mod_time}")
    
    print(f"{'='*60}")
    
    # 检查训练进程是否存在
    import subprocess
    try:
        result = subprocess.run('tasklist | findstr python', shell=True, capture_output=True, text=True)
        if 'python.exe' in result.stdout:
            print("Python进程存在，训练可能仍在进行中")
        else:
            print("未检测到Python进程，训练可能已完成或中断")
    except Exception as e:
        print(f"检查进程时出错: {e}")
    
    return os.path.exists(results_file)

if __name__ == "__main__":
    training_dir = "runs/detect/pv_thermal_detection_s"
    
    # 执行定期检查，直到训练日志出现
    print("开始监控训练进度，每30秒检查一次...")
    print("按Ctrl+C中断监控")
    
    try:
        while True:
            has_results = check_progress(training_dir)
            
            # 如果发现训练日志，则降低检查频率
            sleep_time = 15 if not has_results else 60
            
            print(f"\n等待{sleep_time}秒后再次检查...")
            time.sleep(sleep_time)
    except KeyboardInterrupt:
        print("\n监控已停止") 