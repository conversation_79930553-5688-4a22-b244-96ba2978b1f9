#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
光伏电板热成像故障检测 - 训练平台启动器
"""

import os
import sys
import subprocess

def main():
    """主函数 - 启动训练平台"""
    print("启动光伏电板热成像故障检测训练平台...")
    
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 构建训练平台脚本路径
    trainer_script = os.path.join(script_dir, "scripts", "pv_thermal_trainer.py")
    
    if not os.path.exists(trainer_script):
        print(f"错误: 找不到训练平台脚本: {trainer_script}")
        print("请确保scripts目录中包含pv_thermal_trainer.py文件")
        sys.exit(1)
    
    # 启动训练平台
    try:
        subprocess.run([sys.executable, trainer_script], check=True)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except subprocess.CalledProcessError as e:
        print(f"\n训练平台运行出错，返回代码: {e.returncode}")
    except Exception as e:
        print(f"\n启动训练平台时发生错误: {e}")
    
    print("训练平台已关闭")

if __name__ == "__main__":
    main() 