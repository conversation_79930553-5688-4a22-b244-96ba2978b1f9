"""
分析模型性能脚本
比较CPU和GPU推理速度，分析类别表现
"""

from ultralytics import YOLO
import cv2
import os
import time
import torch
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from collections import defaultdict

def benchmark_model(model_path, test_dir, gpu=True, iterations=5):
    """
    对模型进行基准测试，比较CPU和GPU速度
    """
    # 加载模型
    print(f"加载模型: {model_path}")
    
    # 准备设备
    if gpu and torch.cuda.is_available():
        device = 'cuda:0'
        print(f"使用GPU: {torch.cuda.get_device_name(0)}")
    else:
        device = 'cpu'
        print("使用CPU")
    
    model = YOLO(model_path)
    
    # 获取测试图像
    image_files = [os.path.join(test_dir, f) for f in os.listdir(test_dir) 
                  if f.endswith(('.jpg', '.jpeg', '.png'))]
    if not image_files:
        print(f"错误: 在 {test_dir} 中找不到图像")
        return
    
    # 随机选择图像进行测试
    if len(image_files) > 10:
        np.random.shuffle(image_files)
        test_images = image_files[:10]
    else:
        test_images = image_files
    
    print(f"使用 {len(test_images)} 张图像进行基准测试")
    
    # 测试性能
    times = []
    detections = []
    class_counts = defaultdict(int)
    
    # 预热
    _ = model(test_images[0], device=device)
    
    # 开始测试
    for i, img_path in enumerate(test_images):
        total_time = 0
        
        print(f"测试图像 {i+1}/{len(test_images)}: {os.path.basename(img_path)}")
        
        # 多次迭代减少波动
        for j in range(iterations):
            start_time = time.time()
            results = model(img_path, device=device)[0]
            iter_time = time.time() - start_time
            total_time += iter_time
            
            # 记录第一次迭代的检测结果
            if j == 0:
                detections.append(len(results.boxes))
                
                # 统计类别分布
                for box in results.boxes:
                    cls_id = int(box.cls[0].item())
                    class_name = results.names[cls_id]
                    class_counts[class_name] += 1
        
        # 计算平均时间
        avg_time = total_time / iterations
        times.append(avg_time * 1000)  # 转换为毫秒
        
        print(f"  检测到 {detections[i]} 个对象，平均用时: {avg_time*1000:.1f}ms")
    
    # 输出统计信息
    avg_time = np.mean(times)
    avg_detections = np.mean(detections)
    
    print("\n性能统计:")
    print(f"设备: {device}")
    print(f"平均推理时间: {avg_time:.1f}ms")
    print(f"平均检测对象数: {avg_detections:.1f}")
    print("\n类别分布:")
    for cls_name, count in sorted(class_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"- {cls_name}: {count} 个实例")
    
    # 返回统计信息
    return {
        'device': device,
        'times': times,
        'detections': detections,
        'avg_time': avg_time,
        'avg_detections': avg_detections,
        'class_counts': class_counts
    }

def analyze_class_performance(model_path, img_dirs):
    """
    分析模型在不同类别上的表现
    """
    print("\n分析类别表现...")
    
    # 加载模型
    model = YOLO(model_path)
    
    # 收集类别表现
    class_metrics = defaultdict(lambda: {'tp': 0, 'fp': 0, 'fn': 0, 'conf': []})
    
    # 处理每个目录
    for img_dir, label_dir in img_dirs:
        # 获取所有图像和标签文件
        image_files = [f for f in os.listdir(img_dir) if f.endswith(('.jpg', '.jpeg', '.png'))]
        
        for img_file in image_files:
            # 图像路径
            img_path = os.path.join(img_dir, img_file)
            
            # 标签文件路径
            label_file = os.path.splitext(img_file)[0] + '.txt'
            label_path = os.path.join(label_dir, label_file)
            
            # 如果标签文件不存在，跳过
            if not os.path.exists(label_path):
                continue
            
            # 读取真实标签
            true_boxes = []
            true_classes = []
            with open(label_path, 'r') as f:
                for line in f:
                    parts = line.strip().split()
                    if len(parts) >= 5:
                        cls_id = int(parts[0])
                        x, y, w, h = map(float, parts[1:5])
                        true_boxes.append([x, y, w, h])
                        true_classes.append(cls_id)
            
            # 模型预测
            results = model(img_path)
            pred_boxes = results[0].boxes
            
            # 获取类别名称
            class_names = results[0].names
            
            # 创建已匹配的真实框跟踪器
            matched_true_boxes = [False] * len(true_boxes)
            
            # 检查每个预测框
            for pred_box in pred_boxes:
                pred_cls = int(pred_box.cls[0].item())
                pred_cls_name = class_names[pred_cls]
                conf = float(pred_box.conf[0].item())
                
                class_metrics[pred_cls_name]['conf'].append(conf)
                
                # 检查是否有匹配的真实框
                match_found = False
                for i, (true_cls, true_box) in enumerate(zip(true_classes, true_boxes)):
                    if matched_true_boxes[i]:
                        continue
                    
                    # 如果类别匹配
                    if pred_cls == true_cls:
                        # 标记为已匹配
                        matched_true_boxes[i] = True
                        class_metrics[pred_cls_name]['tp'] += 1
                        match_found = True
                        break
                
                # 如果没有找到匹配
                if not match_found:
                    class_metrics[pred_cls_name]['fp'] += 1
            
            # 计算未匹配的真实框（漏检）
            for i, (matched, true_cls) in enumerate(zip(matched_true_boxes, true_classes)):
                if not matched:
                    true_cls_name = class_names[true_cls]
                    class_metrics[true_cls_name]['fn'] += 1
    
    # 计算每个类别的精确率、召回率和F1分数
    print("\n类别表现:")
    for cls_name, metrics in class_metrics.items():
        tp = metrics['tp']
        fp = metrics['fp']
        fn = metrics['fn']
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        conf_mean = np.mean(metrics['conf']) if metrics['conf'] else 0
        
        print(f"\n- {cls_name}:")
        print(f"  实例数: {tp + fn}")
        print(f"  精确率: {precision:.4f}")
        print(f"  召回率: {recall:.4f}")
        print(f"  F1分数: {f1:.4f}")
        print(f"  置信度平均值: {conf_mean:.4f}")
        print(f"  真阳性: {tp}, 假阳性: {fp}, 假阴性: {fn}")
    
    return class_metrics

def visualize_performance(cpu_stats, gpu_stats=None, output_dir='analysis_results'):
    """
    可视化性能对比
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建性能对比图
    plt.figure(figsize=(12, 6))
    
    # 平均推理时间对比
    plt.subplot(1, 2, 1)
    devices = ['CPU']
    times = [cpu_stats['avg_time']]
    
    if gpu_stats:
        devices.append('GPU')
        times.append(gpu_stats['avg_time'])
    
    bars = plt.bar(devices, times, color=['blue', 'green'] if gpu_stats else ['blue'])
    
    # 在柱状图上添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                 f'{height:.1f}ms', ha='center', va='bottom')
    
    plt.title('平均推理时间对比')
    plt.ylabel('时间 (毫秒)')
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 类别分布
    plt.subplot(1, 2, 2)
    class_names = list(cpu_stats['class_counts'].keys())
    class_counts = list(cpu_stats['class_counts'].values())
    
    # 按计数排序
    sorted_data = sorted(zip(class_names, class_counts), key=lambda x: x[1], reverse=True)
    class_names, class_counts = zip(*sorted_data) if sorted_data else ([], [])
    
    plt.bar(class_names, class_counts, color='orange')
    plt.title('检测到的类别分布')
    plt.ylabel('实例数量')
    plt.xticks(rotation=45, ha='right')
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'performance_comparison.png'))
    print(f"性能对比图已保存到 {os.path.join(output_dir, 'performance_comparison.png')}")
    
def main():
    # 设置路径
    model_path = 'runs/detect/pv_thermal_detection2/weights/best.pt'
    test_dir = 'data/pv_thermal_detection/test/images'
    output_dir = 'analysis_results'
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # CPU测试
    print("\n==== CPU基准测试 ====")
    cpu_stats = benchmark_model(model_path, test_dir, gpu=False)
    
    # GPU测试
    if torch.cuda.is_available():
        print("\n==== GPU基准测试 ====")
        gpu_stats = benchmark_model(model_path, test_dir, gpu=True)
    else:
        print("\nGPU不可用，跳过GPU测试")
        gpu_stats = None
    
    # 可视化性能对比
    visualize_performance(cpu_stats, gpu_stats, output_dir)
    
    # 分析类别表现
    img_dirs = [
        ('data/pv_thermal_detection/test/images', 'data/pv_thermal_detection/test/labels'),
        ('data/pv_thermal_detection/valid/images', 'data/pv_thermal_detection/valid/labels')
    ]
    class_metrics = analyze_class_performance(model_path, img_dirs)
    
    print(f"\n分析结果已保存到 {output_dir} 目录")

if __name__ == "__main__":
    main() 