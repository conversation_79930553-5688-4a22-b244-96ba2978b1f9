"""
全局配置文件
定义项目的所有配置参数
"""

import os
from pathlib import Path
from typing import List, Dict, Any

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
DATA_ROOT = PROJECT_ROOT / "data"
MODEL_ROOT = PROJECT_ROOT / "models"
OUTPUT_ROOT = PROJECT_ROOT / "outputs"

# 数据集配置
class DataConfig:
    """数据集相关配置"""
    
    # 数据集路径
    RAW_DATA_DIR = DATA_ROOT / "raw"
    PROCESSED_DATA_DIR = DATA_ROOT / "processed"
    
    # 数据集分割比例
    TRAIN_RATIO = 0.7
    VAL_RATIO = 0.2
    TEST_RATIO = 0.1
    
    # 图像配置
    IMAGE_SIZE = 640  # 输入图像尺寸
    IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    
    # 类别配置
    CLASS_NAMES = [
        'Diode anomaly',    # 二极管异常
        'Hot Spots',        # 热点
        'Reverse polarity', # 反向极性
        'Vegetation'        # 植被遮挡
    ]
    NUM_CLASSES = len(CLASS_NAMES)
    
    # 类别颜色映射（用于可视化）
    CLASS_COLORS = [
        (255, 0, 0),      # 红色 - 二极管异常
        (255, 165, 0),    # 橙色 - 热点
        (255, 255, 0),    # 黄色 - 反向极性
        (0, 255, 0),      # 绿色 - 植被遮挡
    ]

# 模型配置
class ModelConfig:
    """模型相关配置"""
    
    # 预训练模型路径
    PRETRAINED_DIR = MODEL_ROOT / "pretrained"
    TRAINED_DIR = MODEL_ROOT / "trained"
    EXPORTED_DIR = MODEL_ROOT / "exported"
    
    # YOLO模型配置
    YOLO_MODELS = {
        'yolov8n': 'yolov8n.pt',  # 轻量级模型
        'yolov8s': 'yolov8s.pt',  # 小型模型
        'yolov8m': 'yolov8m.pt',  # 中型模型
        'yolov8l': 'yolov8l.pt',  # 大型模型
        'yolov8x': 'yolov8x.pt',  # 超大型模型
    }
    
    # 默认模型
    DEFAULT_MODEL = 'yolov8n'
    
    # 分层检测配置
    HIERARCHICAL_CONFIG = {
        'light_model': 'yolov8n',  # 第一阶段轻量级模型
        'heavy_model': 'yolov8s',  # 第二阶段高精度模型
        'light_conf': 0.25,        # 轻量级模型置信度阈值
        'heavy_conf': 0.3,         # 高精度模型置信度阈值
        'expand_ratio': 0.1,       # 边界框扩展比例
    }

# 训练配置
class TrainingConfig:
    """训练相关配置"""
    
    # 基础训练参数
    EPOCHS = 100
    BATCH_SIZE = 16
    LEARNING_RATE = 0.01
    WEIGHT_DECAY = 0.0005
    
    # 优化器配置
    OPTIMIZER = 'SGD'  # 可选: 'SGD', 'Adam', 'AdamW'
    MOMENTUM = 0.937
    
    # 学习率调度
    LR_SCHEDULER = 'cosine'  # 可选: 'cosine', 'linear', 'step'
    WARMUP_EPOCHS = 3
    WARMUP_MOMENTUM = 0.8
    WARMUP_BIAS_LR = 0.1
    
    # 早停配置
    PATIENCE = 50
    MIN_DELTA = 0.001
    
    # 数据增强配置
    AUGMENTATION = {
        'degrees': 0.3,      # 旋转角度
        'translate': 0.1,    # 平移比例
        'scale': 0.5,        # 缩放比例
        'shear': 0.2,        # 剪切变换
        'perspective': 0.0,  # 透视变换
        'flipud': 0.0,       # 上下翻转概率
        'fliplr': 0.5,       # 左右翻转概率
        'mosaic': 1.0,       # Mosaic增强概率
        'mixup': 0.0,        # Mixup增强概率
        'copy_paste': 0.0,   # 复制粘贴增强概率
        'hsv_h': 0.015,      # 色调增强
        'hsv_s': 0.7,        # 饱和度增强
        'hsv_v': 0.4,        # 亮度增强
    }
    
    # 保存配置
    SAVE_PERIOD = 10  # 每N个epoch保存一次
    SAVE_BEST_ONLY = True

# 推理配置
class InferenceConfig:
    """推理相关配置"""
    
    # 检测参数
    CONF_THRESHOLD = 0.25    # 置信度阈值
    IOU_THRESHOLD = 0.45     # NMS IoU阈值
    MAX_DETECTIONS = 1000    # 最大检测数量
    
    # 设备配置
    DEVICE = 'auto'  # 'auto', 'cpu', 'cuda', 'mps'
    
    # 输出配置
    SAVE_RESULTS = True
    SAVE_CONFIDENCE = True
    SAVE_CROPS = False

# 日志配置
class LogConfig:
    """日志相关配置"""
    
    LOG_DIR = OUTPUT_ROOT / "logs"
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    
    # 日志文件配置
    LOG_ROTATION = "10 MB"
    LOG_RETENTION = "30 days"

# Web配置
class WebConfig:
    """Web界面配置"""
    
    HOST = "0.0.0.0"
    PORT = 5000
    DEBUG = True
    
    # 上传配置
    UPLOAD_FOLDER = OUTPUT_ROOT / "uploads"
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}

# 创建必要的目录
def create_directories():
    """创建项目所需的目录结构"""
    directories = [
        DATA_ROOT / "raw",
        DATA_ROOT / "processed",
        MODEL_ROOT / "pretrained",
        MODEL_ROOT / "trained", 
        MODEL_ROOT / "exported",
        OUTPUT_ROOT / "logs",
        OUTPUT_ROOT / "results",
        OUTPUT_ROOT / "reports",
        OUTPUT_ROOT / "uploads",
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        
    print("✅ 项目目录结构创建完成")

# 验证配置
def validate_config():
    """验证配置的有效性"""
    assert DataConfig.TRAIN_RATIO + DataConfig.VAL_RATIO + DataConfig.TEST_RATIO == 1.0, \
        "数据集分割比例之和必须等于1.0"
    
    assert DataConfig.NUM_CLASSES == len(DataConfig.CLASS_NAMES), \
        "类别数量与类别名称列表长度不匹配"
    
    assert len(DataConfig.CLASS_COLORS) == DataConfig.NUM_CLASSES, \
        "类别颜色数量与类别数量不匹配"
    
    print("✅ 配置验证通过")

if __name__ == "__main__":
    # 创建目录并验证配置
    create_directories()
    validate_config()
    print("🎉 配置初始化完成！")
