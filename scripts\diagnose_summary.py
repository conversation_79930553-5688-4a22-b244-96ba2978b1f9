import os
import argparse
import sys

def parse_args():
    parser = argparse.ArgumentParser(description="诊断检测摘要文件")
    parser.add_argument("--file", type=str, required=True, help="摘要文件路径")
    return parser.parse_args()

def diagnose_summary_file(file_path):
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在: {file_path}")
        return
    
    print(f"分析文件: {file_path}")
    print("=" * 50)
    
    # 读取文件内容
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            print(f"成功读取文件，共 {len(lines)} 行")
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    # 打印前10行和后10行
    print("\n文件前10行:")
    for i, line in enumerate(lines[:10]):
        print(f"{i+1}: {line.strip()}")
    
    print("\n文件后10行:")
    for i, line in enumerate(lines[-10:], start=len(lines)-10):
        print(f"{i+1}: {line.strip()}")
    
    # 检查文件结构
    print("\n检查文件结构:")
    in_summary = False
    found_stats = False
    
    for i, line in enumerate(lines):
        line = line.strip()
        
        # 识别关键节点
        if line.startswith("模型:"):
            print(f"行 {i+1}: 找到模型信息: {line}")
        elif line.startswith("测试目录:"):
            print(f"行 {i+1}: 找到测试目录: {line}")
        elif line.startswith("置信度阈值:"):
            print(f"行 {i+1}: 找到置信度阈值: {line}")
        elif line.startswith("测试时间:"):
            print(f"行 {i+1}: 找到测试时间: {line}")
        elif line == "## 统计摘要":
            print(f"行 {i+1}: 找到统计摘要标记")
            in_summary = True
        elif in_summary and line.startswith("- 总图像数:"):
            print(f"行 {i+1}: 找到总图像数: {line}")
        elif in_summary and line.startswith("- 平均推理时间:"):
            print(f"行 {i+1}: 找到平均推理时间: {line}")
        elif in_summary and line.startswith("- 各类别检测统计:"):
            print(f"行 {i+1}: 找到类别统计标记")
            found_stats = True
        elif found_stats and line.startswith("  - "):
            print(f"行 {i+1}: 找到类别统计: {line}")
    
    # 统计检测数据
    print("\n提取检测统计数据:")
    detection_counts = {}
    
    # 找到统计部分
    in_stats = False
    for line in lines:
        line = line.strip()
        
        if line == "- 各类别检测统计:":
            in_stats = True
            continue
        
        if in_stats and line.startswith("  - "):
            try:
                # 解析格式 "  - 类别名: 数量 个"
                parts = line.strip("  - ").split(":", 1)
                if len(parts) == 2:
                    class_name = parts[0].strip()
                    count_part = parts[1].strip().split()
                    count = int(count_part[0])
                    detection_counts[class_name] = count
                    print(f"解析到: 类别 '{class_name}' - 数量 {count}")
            except Exception as e:
                print(f"解析行 '{line}' 时出错: {e}")
    
    # 显示统计结果
    if detection_counts:
        print("\n成功解析的检测统计:")
        for class_name, count in detection_counts.items():
            print(f"  - {class_name}: {count}")
    else:
        print("\n未能解析任何检测统计数据")

if __name__ == "__main__":
    print(f"Python 版本: {sys.version}")
    print(f"脚本路径: {__file__}")
    args = parse_args()
    diagnose_summary_file(args.file) 