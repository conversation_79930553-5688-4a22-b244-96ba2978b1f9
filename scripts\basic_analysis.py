import os
import sys
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import shutil
import argparse

def parse_args():
    parser = argparse.ArgumentParser(description='分析YOLOv8训练结果')
    parser.add_argument('--results', type=str, default='runs/detect/pv_anomaly_yolov8s4', 
                        help='训练结果目录')
    parser.add_argument('--csv', type=str, help='CSV结果文件路径', default='')
    parser.add_argument('--output-dir', type=str, default='models/analysis/pv_anomaly_yolov8s', help='分析结果输出目录')
    return parser.parse_args()

def plot_training_curves(results_csv, output_dir):
    """绘制训练曲线"""
    if not os.path.exists(results_csv):
        print(f"未找到训练结果CSV文件: {results_csv}")
        return None
    
    try:
        # 读取结果文件
        results_df = pd.read_csv(results_csv)
        
        # 创建图表
        fig, axs = plt.subplots(2, 2, figsize=(15, 10))
        
        # 绘制损失曲线
        epochs = results_df['epoch'].values
        train_box_loss = results_df['train/box_loss'].values
        train_cls_loss = results_df['train/cls_loss'].values
        train_dfl_loss = results_df['train/dfl_loss'].values
        
        axs[0, 0].plot(epochs, train_box_loss, label='box损失')
        axs[0, 0].plot(epochs, train_cls_loss, label='分类损失')
        axs[0, 0].plot(epochs, train_dfl_loss, label='DFL损失')
        axs[0, 0].set_xlabel('轮数')
        axs[0, 0].set_ylabel('损失')
        axs[0, 0].set_title('训练损失')
        axs[0, 0].legend()
        axs[0, 0].grid(True)
        
        # 绘制mAP@0.5曲线
        mAP50 = results_df['metrics/mAP50(B)'].values
        axs[0, 1].plot(epochs, mAP50, label='mAP@0.5', color='green')
        axs[0, 1].set_xlabel('轮数')
        axs[0, 1].set_ylabel('mAP@0.5')
        axs[0, 1].set_title('模型mAP@0.5性能')
        axs[0, 1].legend()
        axs[0, 1].grid(True)
        
        # 绘制mAP@0.5:0.95曲线
        mAP50_95 = results_df['metrics/mAP50-95(B)'].values
        axs[1, 0].plot(epochs, mAP50_95, label='mAP@0.5:0.95', color='purple')
        axs[1, 0].set_xlabel('轮数')
        axs[1, 0].set_ylabel('mAP@0.5:0.95')
        axs[1, 0].set_title('模型mAP@0.5:0.95性能')
        axs[1, 0].legend()
        axs[1, 0].grid(True)
        
        # 绘制精确率和召回率
        precision = results_df['metrics/precision(B)'].values
        recall = results_df['metrics/recall(B)'].values
        axs[1, 1].plot(epochs, precision, label='精确率', color='red')
        axs[1, 1].plot(epochs, recall, label='召回率', color='blue')
        axs[1, 1].set_xlabel('轮数')
        axs[1, 1].set_ylabel('值')
        axs[1, 1].set_title('精确率和召回率')
        axs[1, 1].legend()
        axs[1, 1].grid(True)
        
        # 保存并显示图表
        plt.tight_layout()
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, 'training_curves.png')
        plt.savefig(output_path)
        plt.close(fig)
        
        print(f"训练曲线已保存到 {output_path}")
        return output_path
    except Exception as e:
        print(f"绘制训练曲线时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_training_results(results_csv, output_dir):
    """分析训练结果并生成摘要报告"""
    if not os.path.exists(results_csv):
        print(f"错误: 结果文件不存在: {results_csv}")
        return False
    
    try:
        # 读取CSV结果文件
        results = pd.read_csv(results_csv)
        
        # 输出文件路径
        os.makedirs(output_dir, exist_ok=True)
        output_txt = os.path.join(output_dir, 'training_summary.txt')
        
        print(f"正在分析训练结果: {results_csv}")
        print(f"总共训练了 {len(results)} 个epoch")
        
        # 获取关键指标
        final_epoch = results.iloc[-1]
        best_map = results['metrics/mAP50(B)'].max()
        best_map_epoch = results['metrics/mAP50(B)'].idxmax() + 1  # +1因为epoch从1开始
        
        best_map50_95 = results['metrics/mAP50-95(B)'].max()
        best_map50_95_epoch = results['metrics/mAP50-95(B)'].idxmax() + 1
        
        # 创建摘要报告
        with open(output_txt, 'w') as f:
            f.write("# 光伏异常检测YOLOv8s模型训练结果\n\n")
            
            f.write("## 训练摘要\n")
            f.write(f"总训练轮次: {len(results)}\n")
            f.write(f"最佳mAP50: {best_map:.4f} (第{best_map_epoch}轮)\n")
            f.write(f"最佳mAP50-95: {best_map50_95:.4f} (第{best_map50_95_epoch}轮)\n\n")
            
            f.write("## 最终轮次性能\n")
            f.write(f"mAP50: {final_epoch['metrics/mAP50(B)']:.4f}\n")
            f.write(f"mAP50-95: {final_epoch['metrics/mAP50-95(B)']:.4f}\n")
            f.write(f"精度(Precision): {final_epoch['metrics/precision(B)']:.4f}\n")
            f.write(f"召回率(Recall): {final_epoch['metrics/recall(B)']:.4f}\n\n")
            
            f.write("## 每10个轮次的性能\n")
            f.write("轮次\tmAP50\tmAP50-95\t精度\t召回率\n")
            for i in range(0, len(results), 10):
                if i < len(results):
                    epoch = results.iloc[i]
                    f.write(f"{i+1}\t{epoch['metrics/mAP50(B)']:.4f}\t{epoch['metrics/mAP50-95(B)']:.4f}\t{epoch['metrics/precision(B)']:.4f}\t{epoch['metrics/recall(B)']:.4f}\n")
        
        print(f"训练分析摘要已保存至: {output_txt}")
        return True
    except Exception as e:
        print(f"分析结果时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def copy_visualization_files(source_dir, target_dir):
    """复制训练生成的可视化文件到目标目录"""
    try:
        source_path = Path(source_dir)
        os.makedirs(target_dir, exist_ok=True)
        
        # 查找可视化图像
        visualization_files = [
            source_path / 'labels.jpg',
            source_path / 'labels_correlogram.jpg',
            source_path / 'train_batch0.jpg',
            source_path / 'train_batch1.jpg',
            source_path / 'train_batch2.jpg'
        ]
        
        copied_count = 0
        for file_path in visualization_files:
            if file_path.exists():
                target_path = os.path.join(target_dir, file_path.name)
                shutil.copy(file_path, target_path)
                print(f"复制 {file_path} -> {target_path}")
                copied_count += 1
        
        # 查找结果文件
        results_files = list(source_path.glob('results.*'))
        for file_path in results_files:
            target_path = os.path.join(target_dir, file_path.name)
            shutil.copy(file_path, target_path)
            print(f"复制 {file_path} -> {target_path}")
            copied_count += 1
        
        # 复制混淆矩阵等文件
        confusion_files = list(source_path.glob('confusion_matrix*'))
        for file_path in confusion_files:
            target_path = os.path.join(target_dir, file_path.name)
            shutil.copy(file_path, target_path)
            print(f"复制 {file_path} -> {target_path}")
            copied_count += 1
        
        # 复制PR曲线等文件
        pr_files = list(source_path.glob('PR_curve*'))
        for file_path in pr_files:
            target_path = os.path.join(target_dir, file_path.name)
            shutil.copy(file_path, target_path)
            print(f"复制 {file_path} -> {target_path}")
            copied_count += 1
        
        # 复制F1曲线等文件
        f1_files = list(source_path.glob('F1_curve*'))
        for file_path in f1_files:
            target_path = os.path.join(target_dir, file_path.name)
            shutil.copy(file_path, target_path)
            print(f"复制 {file_path} -> {target_path}")
            copied_count += 1
        
        if copied_count > 0:
            print(f"已复制 {copied_count} 个可视化文件到 {target_dir}")
        else:
            print(f"未找到可复制的可视化文件")
        
        return copied_count > 0
    except Exception as e:
        print(f"复制可视化文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    # 解析命令行参数
    args = parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    print(f"分析结果将保存到: {args.output_dir}")
    
    # 确定CSV结果文件路径
    if args.csv:
        results_csv = args.csv
    else:
        # 尝试从results目录查找
        results_csv = os.path.join(args.results, 'results.csv')
    
    if not os.path.exists(results_csv):
        print(f"未找到训练结果CSV文件: {results_csv}")
        results_csv = None
    else:
        print(f"使用训练结果文件: {results_csv}")
        
        # 分析训练结果并生成摘要
        analyze_training_results(results_csv, args.output_dir)
        
        # 绘制训练曲线
        plot_training_curves(results_csv, args.output_dir)
    
    # 复制训练过程中生成的可视化文件
    copy_visualization_files(args.results, args.output_dir)
    
    print(f"\n分析完成！结果已保存到: {args.output_dir}")

if __name__ == "__main__":
    main() 