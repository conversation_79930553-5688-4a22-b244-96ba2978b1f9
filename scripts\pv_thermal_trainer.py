#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
光伏电板热成像故障检测 - 统一训练平台
将各种训练和分析功能整合到一个统一的界面中
"""

import os
import sys
import subprocess
import time
import torch
import psutil
import gc
import shutil
import yaml
from pathlib import Path
from datetime import datetime

# 添加scripts目录到PATH
SCRIPTS_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(SCRIPTS_DIR)

def clear_screen():
    """清理控制台屏幕"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_banner():
    """打印程序横幅"""
    banner = """
==============================================================================
            光伏电板热成像故障检测 - 统一训练平台 v1.0
==============================================================================
    """
    print(banner)

def show_menu():
    """显示主菜单"""
    print_banner()
    print("请选择要训练的模型类型：")
    print("1. YOLOv8n (较小模型，适合低配置GPU或边缘设备)")
    print("2. YOLOv8s (中等模型，平衡性能和速度)")
    print("3. YOLOv8m (中大型模型，更高精度)")
    print("4. YOLOv8l (大型模型，高精度)")
    print("5. YOLOv8x (超大型模型，最高精度)")
    print("6. YOLOv8混合模型 (使用YOLOv8s的骨干网络和YOLOv8n的检测头)")
    print("7. 安装缺失的依赖")
    print("8. 清理训练缓存和进程")
    print("9. 分析现有的训练结果")
    print("0. 退出")
    
    choice = input("\n请输入选项 [0-9]: ")
    return choice

def check_system_resources():
    """检查系统资源"""
    # 检查GPU
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        print(f"✓ GPU: {gpu_name} ({gpu_memory:.2f} GB)")
        
        # 根据GPU内存推荐模型
        if gpu_memory < 4:
            print("  推荐: YOLOv8n (您的GPU内存较小)")
        elif gpu_memory < 8:
            print("  推荐: YOLOv8s/YOLOv8n (中等GPU内存)")
        else:
            print("  推荐: 任何YOLOv8模型 (您有足够的GPU内存)")
    else:
        print("✗ GPU不可用，将使用CPU训练(速度会非常慢)")
    
    # 检查CPU
    cpu_count = psutil.cpu_count(logical=True)
    print(f"✓ CPU: {cpu_count}核")
    
    # 检查内存
    vm = psutil.virtual_memory()
    print(f"✓ 系统内存: 总计={vm.total/(1024**3):.2f}GB, 可用={vm.available/(1024**3):.2f}GB, 使用率={vm.percent}%")
    
    if vm.percent > 90:
        print("  警告: 系统内存使用率超过90%，训练可能会受到影响")
    
    # 检查磁盘空间
    disk = psutil.disk_usage('/')
    print(f"✓ 磁盘空间: 总计={disk.total/(1024**3):.2f}GB, 可用={disk.free/(1024**3):.2f}GB, 使用率={disk.percent}%")
    
    if disk.percent > 90:
        print("  警告: 磁盘空间不足，可能影响训练结果保存")

def check_and_terminate_processes():
    """检查是否有其他Python进程正在运行并询问是否终止"""
    python_procs = [p for p in psutil.process_iter(['pid', 'name']) 
                  if 'python' in p.info['name'].lower()]
    
    if len(python_procs) > 1:  # 1是当前进程
        print(f"警告: 发现 {len(python_procs)-1} 个其他Python进程正在运行")
        for proc in python_procs:
            if proc.pid != os.getpid():
                print(f"PID: {proc.pid}, 名称: {proc.info['name']}")
        
        response = input("是否终止这些进程? (y/n): ")
        if response.lower() == 'y':
            for proc in python_procs:
                if proc.pid != os.getpid():
                    try:
                        proc.kill()
                        print(f"已终止进程 {proc.pid}")
                    except:
                        print(f"无法终止进程 {proc.pid}")
            print("已终止所有其他Python进程")

def free_memory():
    """手动释放内存"""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    print("已手动清理内存")

def create_config_file(model_type, epochs, batch, imgsz, model_name):
    """创建训练配置文件"""
    # 确定基础模型名称
    if model_type == "hybrid":
        base_model = "models/yolov8_hybrid.pt"
        # 检查是否需要创建混合模型
        if not os.path.exists(base_model):
            print("混合模型文件不存在，需要先创建...")
            # 创建混合模型目录
            os.makedirs(os.path.dirname(base_model), exist_ok=True)
            
            # 创建混合模型
            try:
                # 调用create_hybrid_model.py脚本创建混合模型
                hybrid_script = os.path.join(SCRIPTS_DIR, "create_hybrid_model.py")
                if not os.path.exists(hybrid_script):
                    print(f"错误: 混合模型创建脚本不存在: {hybrid_script}")
                    base_model = "yolov8s.pt"  # 使用标准模型作为后备
                else:
                    cmd = [
                        "python", hybrid_script,
                        "--small", "yolov8n.pt", 
                        "--large", "yolov8s.pt",
                        "--output", base_model
                    ]
                    print(f"执行命令: {' '.join(cmd)}")
                    result = subprocess.run(cmd, check=False)
                    
                    if result.returncode != 0:
                        print("创建混合模型失败，将使用标准模型代替")
                        base_model = "yolov8s.pt"
            except Exception as e:
                print(f"创建混合模型时出错: {str(e)}")
                base_model = "yolov8s.pt"  # 使用标准模型作为后备
    else:
        base_model = f"yolov8{model_type}.pt"
    
    # 设置图像尺寸 (根据模型类型调整，大模型使用更大的尺寸)
    if model_type in ["m", "l", "x"]:
        if int(imgsz) < 640:
            print(f"注意: 对于YOLOv8{model_type}模型，建议使用更大的图像尺寸 (>=640)")
            imgsz = "640" if input("是否调整为640? (y/n): ").lower() == "y" else imgsz
    
    # 设置批量大小 (根据模型类型和GPU内存调整)
    if model_type in ["l", "x"] and torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        if gpu_memory < 8 and int(batch) > 2:
            print(f"警告: 对于YOLOv8{model_type}模型和{gpu_memory:.1f}GB显存，建议使用较小的批量大小")
            batch = "2" if input("是否调整为2? (y/n): ").lower() == "y" else batch
    
    # 创建配置目录
    os.makedirs("configs", exist_ok=True)
    
    # 配置文件路径
    config_path = f"configs/train_config_{model_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
    
    # 基本配置
    config = {
        "batch": int(batch),
        "data": "data/pv_thermal_detection/data.yaml",
        "epochs": int(epochs),
        "imgsz": int(imgsz),
        "model": base_model,
        "name": model_name,
        "project": "runs/detect",
        "device": 0 if torch.cuda.is_available() else "cpu",
        
        # 训练参数
        "lr0": 0.01,
        "lrf": 0.01,
        "momentum": 0.937,
        "weight_decay": 0.0005,
        "warmup_epochs": 3.0,
        "warmup_momentum": 0.8,
        "warmup_bias_lr": 0.1,
        "patience": 50,
        
        # 数据增强
        "degrees": 0.3,
        "fliplr": 0.5,
        "flipud": 0.0,
        "hsv_h": 0.015,
        "hsv_s": 0.7,
        "hsv_v": 0.4,
        "scale": 0.5,
        "translate": 0.1,
        "mosaic": 1.0,
        "mixup": 0.0,
        
        # 其他选项
        "deterministic": True,
        "exist_ok": False,
        "pretrained": True,
        "save": True,
        "save_period": 10,
        "seed": 0,
        "verbose": True,
        "workers": 2
    }
    
    # 为大型模型添加高级优化选项
    if model_type in ["m", "l", "x", "hybrid"]:
        config.update({
            "cos_lr": True,
            "close_mosaic": 10,
            "copy_paste": 0.1,
            "perspective": 0.0003,
            "shear": 0.2,
            "auto_augment": "randaugment",
            "erasing": 0.4
        })
    
    # 保存配置文件
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    print(f"训练配置文件已创建: {config_path}")
    return config_path

def train_model(config_path, model_name, model_type):
    """训练模型"""
    start_time = time.time()
    
    print("\n" + "="*80)
    print(f"开始训练YOLOv8{model_type}模型: {model_name}")
    print("="*80)
    
    # 检查配置文件是否存在
    if not os.path.exists(config_path):
        print(f"错误: 配置文件不存在: {config_path}")
        return False
    
    # 检查训练脚本是否存在
    train_script = os.path.join(SCRIPTS_DIR, "train_model.py")
    if not os.path.exists(train_script):
        print(f"错误: 训练脚本不存在: {train_script}")
        return False
    
    # 设置环境变量限制CUDA内存使用
    if torch.cuda.is_available():
        os.environ['CUDA_VISIBLE_DEVICES'] = '0'
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128,garbage_collection_threshold:0.6'
    
    # 训练模型
    cmd = ["python", train_script, "--cfg", config_path]
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=False)
        
        if result.returncode != 0:
            print(f"\n训练失败，返回代码: {result.returncode}")
            return False
        else:
            print("\n训练完成！")
            return True
    except KeyboardInterrupt:
        print("\n训练被用户中断")
        return False
    except Exception as e:
        print(f"\n训练过程中发生错误: {e}")
        return False
    finally:
        # 计算训练时间
        end_time = time.time()
        duration = end_time - start_time
        hours, remainder = divmod(duration, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        print(f"\n总训练时间: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")

def analyze_results(model_name, model_type, data_path="data/pv_thermal_detection/data.yaml"):
    """分析训练结果"""
    print("\n" + "="*80)
    print(f"分析训练结果: {model_name}")
    print("="*80)
    
    # 训练结果目录
    results_dir = f"runs/detect/{model_name}"
    
    if not os.path.exists(results_dir):
        print(f"错误: 找不到训练结果目录: {results_dir}")
        return False
    
    # 首先尝试使用固定的分析脚本
    analyze_script = os.path.join(SCRIPTS_DIR, "analyze_results_fixed.py")
    
    # 如果固定脚本不存在，尝试使用常规分析脚本
    if not os.path.exists(analyze_script):
        analyze_script = os.path.join(SCRIPTS_DIR, "analyze_results.py")
        if not os.path.exists(analyze_script):
            print(f"错误: 分析脚本不存在: {analyze_script}")
            return False
    
    # 使用分析脚本分析结果
    cmd = [
        "python", analyze_script,
        "--results_dir", results_dir,
        "--data_config", data_path,
        "--conf_threshold", "0.25",
        "--max_samples", "10"
    ]
    
    try:
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=False)
        
        if result.returncode != 0:
            print(f"分析结果失败，返回代码: {result.returncode}")
            return False
        else:
            print("结果分析完成！")
            return True
    except Exception as e:
        print(f"分析结果时出错: {e}")
        return False

def organize_results(model_name, model_type):
    """将训练结果整理到models目录，符合现有项目结构"""
    print("\n" + "="*80)
    print(f"整理训练结果到models目录: {model_name}")
    print("="*80)
    
    # 训练结果目录
    results_dir = f"runs/detect/{model_name}"
    
    if not os.path.exists(results_dir):
        print(f"错误: 找不到训练结果目录: {results_dir}")
        return False
    
    # 创建标准化的文件名前缀 (例如: yolov8n_pv_thermal_20230425_102030)
    filename_prefix = f"yolov8{model_type}_pv_thermal_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # 1. 创建必要的目录
    for directory in ["models/trained", "models/analysis", "models/exported", "models/detection_results"]:
        os.makedirs(directory, exist_ok=True)
    
    # 2. 复制训练模型到trained目录
    weights_dir = os.path.join(results_dir, "weights")
    if os.path.exists(weights_dir):
        # 复制best.pt
        src_best = os.path.join(weights_dir, "best.pt")
        if os.path.exists(src_best):
            dst_best = os.path.join("models/trained", f"{filename_prefix}_best.pt")
            shutil.copy2(src_best, dst_best)
            print(f"已复制训练模型: {src_best} -> {dst_best}")
        
        # 复制last.pt
        src_last = os.path.join(weights_dir, "last.pt")
        if os.path.exists(src_last):
            dst_last = os.path.join("models/trained", f"{filename_prefix}_last.pt")
            shutil.copy2(src_last, dst_last)
            print(f"已复制训练模型: {src_last} -> {dst_last}")
    
    # 3. 复制分析结果到analysis目录
    analysis_dir = os.path.join("models/analysis", f"yolov8{model_type}")
    os.makedirs(analysis_dir, exist_ok=True)
    
    # 复制分析图表
    for result_file in ["class_performance.png", "training_curves.png", "confusion_matrix.png"]:
        src = os.path.join(results_dir, result_file)
        if os.path.exists(src):
            dst = os.path.join(analysis_dir, f"{filename_prefix}_{result_file}")
            shutil.copy2(src, dst)
            print(f"已复制分析结果: {src} -> {dst}")
    
    # 复制性能报告
    for report_file in ["model_performance.txt", "training_summary.txt"]:
        src = os.path.join(results_dir, report_file)
        if os.path.exists(src):
            dst = os.path.join(analysis_dir, f"{filename_prefix}_{report_file}")
            shutil.copy2(src, dst)
            print(f"已复制性能报告: {src} -> {dst}")
    
    # 4. 导出并保存ONNX模型到exported目录
    try:
        # 检查模型导出脚本是否存在
        export_script = os.path.join(SCRIPTS_DIR, "export_model.py")
        
        # 如果导出脚本存在，使用脚本导出模型
        if os.path.exists(export_script):
            best_model_path = os.path.join("models/trained", f"{filename_prefix}_best.pt")
            export_cmd = [
                "python", export_script,
                "--model", best_model_path,
                "--format", "onnx", 
                "--output", f"models/exported/{filename_prefix}.onnx"
            ]
            print(f"执行命令: {' '.join(export_cmd)}")
            subprocess.run(export_cmd, check=False)
            
            # 复制到树莓派部署目录
            raspi_dir = os.path.join("models/exported/raspberry_pi")
            os.makedirs(raspi_dir, exist_ok=True)
            
            # 复制ONNX模型
            onnx_path = f"models/exported/{filename_prefix}.onnx"
            if os.path.exists(onnx_path):
                raspi_onnx = os.path.join(raspi_dir, f"{filename_prefix}.onnx")
                shutil.copy2(onnx_path, raspi_onnx)
                print(f"已复制ONNX模型到树莓派部署目录: {raspi_onnx}")
        else:
            # 如果导出脚本不存在，尝试使用直接API导出
            print("导出脚本不存在，尝试使用API导出模型...")
            best_model_path = os.path.join("models/trained", f"{filename_prefix}_best.pt")
            
            if os.path.exists(best_model_path):
                from ultralytics import YOLO
                model = YOLO(best_model_path)
                # 导出ONNX模型
                onnx_path = model.export(format="onnx", dynamic=True, simplify=True)
                
                # 复制到exported目录
                dst_onnx = os.path.join("models/exported", f"{filename_prefix}.onnx")
                shutil.copy2(onnx_path, dst_onnx)
                print(f"已导出ONNX模型: {dst_onnx}")
                
                # 复制到树莓派目录
                raspi_dir = os.path.join("models/exported/raspberry_pi")
                os.makedirs(raspi_dir, exist_ok=True)
                raspi_onnx = os.path.join(raspi_dir, f"{filename_prefix}.onnx")
                shutil.copy2(onnx_path, raspi_onnx)
                print(f"已复制ONNX模型到树莓派部署目录: {raspi_onnx}")
    except Exception as e:
        print(f"导出ONNX模型时出错: {e}")
    
    # 5. 创建模型信息文件
    model_info = {
        "name": model_name,
        "type": f"YOLOv8{model_type}",
        "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "filename_prefix": filename_prefix,
        "training_params": {
            "results_dir": results_dir,
            "trained_model": f"{filename_prefix}_best.pt",
            "exported_model": f"{filename_prefix}.onnx"
        }
    }
    
    # 保存模型信息
    info_path = os.path.join(analysis_dir, f"{filename_prefix}_info.yaml")
    with open(info_path, 'w', encoding='utf-8') as f:
        yaml.dump(model_info, f, default_flow_style=False)
    
    print(f"模型信息已保存到: {info_path}")
    
    print("\n结果整理完成!")
    print(f"- 训练模型: models/trained/{filename_prefix}_best.pt")
    print(f"- 分析结果: models/analysis/yolov8{model_type}/")
    print(f"- 导出模型: models/exported/{filename_prefix}.onnx")
    
    return True 

def install_dependencies():
    """安装所需依赖"""
    print("\n" + "="*80)
    print("安装缺失的依赖...")
    print("="*80)
    
    # 基本依赖
    subprocess.run(["pip", "install", "ultralytics", "psutil", "torch", "torchvision", "matplotlib", "pyyaml"])
    
    # ONNX相关依赖（用于模型导出）
    subprocess.run(["pip", "install", "onnx", "onnxruntime-gpu", "onnxslim"])
    
    print("\n依赖安装完成！")
    input("\n按Enter键返回主菜单...")

def clean_cache():
    """清理训练缓存和进程"""
    print("\n" + "="*80)
    print("清理训练缓存和进程...")
    print("="*80)
    
    # 终止所有Python进程（除了当前进程）
    if os.name == 'nt':  # Windows
        subprocess.run(["taskkill", "/F", "/IM", "python.exe"], stderr=subprocess.PIPE)
    else:  # Linux/Mac
        subprocess.run(["pkill", "-9", "python"], stderr=subprocess.PIPE)
    
    # 清理缓存文件
    cache_paths = [
        "data/pv_thermal_detection/train/labels.cache",
        "data/pv_thermal_detection/valid/labels.cache",
        "data/pv_thermal_detection/test/labels.cache"
    ]
    
    for path in cache_paths:
        if os.path.exists(path):
            os.remove(path)
            print(f"已删除缓存文件: {path}")
    
    # 清理CUDA缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print("已清理CUDA缓存")
    
    # 清理系统缓存
    gc.collect()
    print("已清理系统缓存")
    
    # 可选：使用cleanup_project.py脚本进行更彻底的清理
    cleanup_script = os.path.join(SCRIPTS_DIR, "cleanup_project.py")
    if os.path.exists(cleanup_script):
        try:
            print("运行项目清理脚本...")
            subprocess.run(["python", cleanup_script], check=False)
        except Exception as e:
            print(f"运行清理脚本时出错: {e}")
    
    print("\n清理完成！")
    input("\n按Enter键返回主菜单...")

def analyze_existing_results():
    """分析现有训练结果"""
    print("\n" + "="*80)
    print("分析现有训练结果")
    print("="*80)
    
    # 列出所有训练结果目录
    results_dir = "runs/detect"
    if not os.path.exists(results_dir):
        print("未找到任何训练结果目录")
        input("\n按Enter键返回主菜单...")
        return
    
    # 获取所有子目录
    result_dirs = [d for d in os.listdir(results_dir) if os.path.isdir(os.path.join(results_dir, d))]
    
    if not result_dirs:
        print("未找到任何训练结果")
        input("\n按Enter键返回主菜单...")
        return
    
    # 按修改时间排序
    result_dirs.sort(key=lambda x: os.path.getmtime(os.path.join(results_dir, x)), reverse=True)
    
    # 显示最近的训练结果
    print("最近的训练结果:")
    for i, dir_name in enumerate(result_dirs[:10]):  # 显示最近的10个
        dir_path = os.path.join(results_dir, dir_name)
        mod_time = datetime.fromtimestamp(os.path.getmtime(dir_path))
        print(f"{i+1}. {dir_name} (修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')})")
    
    # 用户选择
    choice = input("\n请选择要分析的结果 [1-10] 或输入'0'返回: ")
    
    if choice == '0' or not choice.isdigit() or int(choice) < 1 or int(choice) > len(result_dirs[:10]):
        return
    
    selected_dir = result_dirs[int(choice)-1]
    selected_path = os.path.join(results_dir, selected_dir)
    
    # 提取模型类型 (假设目录名格式为 pv_thermal_TYPE_TIMESTAMP)
    model_type = "s"  # 默认为s类型
    parts = selected_dir.split('_')
    if len(parts) >= 3 and parts[0] == "pv" and parts[1] == "thermal":
        if parts[2] in ['n', 's', 'm', 'l', 'x', 'hybrid']:
            model_type = parts[2]
    
    # 分析结果
    print(f"\n分析训练结果: {selected_dir}")
    
    # 使用分析脚本分析结果
    analyze_results(selected_dir, model_type)
    
    # 解释混淆矩阵
    print("\n混淆矩阵解释：")
    print("- 行表示实际的类别（真实标签）")
    print("- 列表示预测的类别")
    print("- 对角线元素表示正确预测的数量")
    print("- 其他元素表示错误分类的数量")
    
    # 例如:
    print("\n假设我们看到混淆矩阵如下:")
    print("- Diode anomaly行的25表示有25个二极管异常被正确检测")
    print("- Hot Spots行的21表示有21个热点被正确检测")
    print("- 如果在'Vegetation'行和'Hot Spots'列有数字(如2)，表示有2个实际是植被的区域被错误地识别为热点")
    
    # 解释其他指标
    print("\n其他重要指标：")
    print("1. mAP (平均精度均值): 检测精确度的综合指标，越高越好")
    print("2. Precision (精确率): 在所有被识别为正类的样本中，真正的正类所占比例")
    print("3. Recall (召回率): 在所有实际为正类的样本中，被正确识别的比例")
    
    input("\n按Enter键返回主菜单...")

def train_selected_model(model_type):
    """训练选定的模型类型"""
    print_banner()
    print(f"正在准备训练YOLOv8{model_type}模型...")
    
    # 检查系统资源
    check_system_resources()
    
    # 检查是否有其他Python进程运行
    check_and_terminate_processes()
    
    # 手动释放内存
    free_memory()
    
    # 获取训练参数
    epochs = input("请输入训练轮数 [默认50]: ") or "50"
    batch = input("请输入批量大小 [默认4]: ") or "4"
    imgsz = input("请输入图像尺寸 [默认640]: ") or "640"
    
    # 生成模型名称
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_name = f"pv_thermal_{model_type}_{timestamp}"
    
    # 创建配置文件
    config_path = create_config_file(model_type, epochs, batch, imgsz, model_name)
    
    # 训练模型
    success = train_model(config_path, model_name, model_type)
    
    if success:
        # 分析结果
        analyze_results(model_name, model_type)
        
        # 整理结果
        organize_results(model_name, model_type)
        
        print("\n" + "="*80)
        print(f"YOLOv8{model_type}模型训练和评估流程已成功完成!")
        print("="*80)
        
        print("\n模型文件位置:")
        print(f"- 训练模型: models/trained/yolov8{model_type}_pv_thermal_{timestamp}_best.pt")
        print(f"- 分析结果: models/analysis/yolov8{model_type}/")
        print(f"- 导出模型: models/exported/yolov8{model_type}_pv_thermal_{timestamp}.onnx")
    
    input("\n按Enter键返回主菜单...")

def main():
    """主函数"""
    # 确保UTF-8输出
    if sys.platform.startswith('win'):
        try:
            sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        except AttributeError:
            # Python 3.6及更低版本没有reconfigure方法
            pass
    
    # 创建必要的目录
    for directory in ["models", "configs", "runs/detect"]:
        os.makedirs(directory, exist_ok=True)
    
    while True:
        choice = show_menu()
        
        if choice == '0':
            print("退出程序...")
            sys.exit(0)
        elif choice == '1':
            train_selected_model("n")
        elif choice == '2':
            train_selected_model("s")
        elif choice == '3':
            train_selected_model("m")
        elif choice == '4':
            train_selected_model("l")
        elif choice == '5':
            train_selected_model("x")
        elif choice == '6':
            train_selected_model("hybrid")
        elif choice == '7':
            install_dependencies()
        elif choice == '8':
            clean_cache()
        elif choice == '9':
            analyze_existing_results()
        else:
            print("无效选项，请重新选择")
            time.sleep(1)
        
        clear_screen()

if __name__ == "__main__":
    main() 