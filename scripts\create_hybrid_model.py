import torch
from ultralytics import YOLO
import os
import yaml
import shutil
import argparse
from pathlib import Path

def parse_args():
    parser = argparse.ArgumentParser(description='创建YOLOv8混合模型')
    parser.add_argument('--s_model', type=str, default='yolov8s.pt', help='YOLOv8s模型路径')
    parser.add_argument('--n_model', type=str, default='yolov8n.pt', help='YOLOv8n模型路径')
    parser.add_argument('--output', type=str, default='yolov8_hybrid.pt', help='输出模型路径')
    parser.add_argument('--backbone_layers', type=int, default=10, help='使用YOLOv8s的骨干网络层数')
    return parser.parse_args()

def create_hybrid_model():
    args = parse_args()
    
    print(f"创建混合模型: YOLOv8s骨干网络 + YOLOv8n检测头")
    
    # 加载模型
    model_s = YOLO(args.s_model)
    model_n = YOLO(args.n_model)
    
    # 检查两个模型是否已下载
    if not os.path.exists(args.s_model) and not os.path.exists(model_s.ckpt_path):
        print(f"下载YOLOv8s模型...")
        model_s = YOLO('yolov8s.pt')
    
    if not os.path.exists(args.n_model) and not os.path.exists(model_n.ckpt_path):
        print(f"下载YOLOv8n模型...")
        model_n = YOLO('yolov8n.pt')
    
    # 获取状态字典
    s_state_dict = model_s.model.state_dict()
    n_state_dict = model_n.model.state_dict()
    
    # 创建一个新的YOLOv8n模型
    hybrid_model = YOLO('yolov8n.pt')
    hybrid_dict = hybrid_model.model.state_dict()
    
    # 合并状态字典 - 使用YOLOv8s的骨干网络和YOLOv8n的检测头
    backbone_layers = args.backbone_layers
    print(f"使用YOLOv8s的前{backbone_layers}层作为骨干网络")
    
    # 保存模型层名称供参考
    s_layers = list(s_state_dict.keys())
    n_layers = list(n_state_dict.keys())
    
    with open('s_model_layers.txt', 'w') as f:
        for i, layer in enumerate(s_layers):
            f.write(f"{i}: {layer}\n")
    
    with open('n_model_layers.txt', 'w') as f:
        for i, layer in enumerate(n_layers):
            f.write(f"{i}: {layer}\n")
    
    print(f"模型层名称已保存到s_model_layers.txt和n_model_layers.txt")
    
    # 创建混合状态字典
    hybrid_dict = {}
    
    # 定义要从YOLOv8s复制的骨干层模式
    backbone_prefixes = [
        'model.0', 'model.1', 'model.2', 'model.3', 'model.4', 
        'model.5', 'model.6', 'model.7', 'model.8', 'model.9'
    ]
    
    # 从s模型复制骨干网络层
    for k, v in s_state_dict.items():
        if any(k.startswith(prefix) for prefix in backbone_prefixes[:backbone_layers]):
            # 检查维度是否匹配
            if k in n_state_dict and v.shape != n_state_dict[k].shape:
                print(f"跳过不匹配的层: {k}, s形状: {v.shape}, n形状: {n_state_dict[k].shape}")
                continue
            hybrid_dict[k] = v
    
    # 从n模型复制其他层
    for k, v in n_state_dict.items():
        if not any(k.startswith(prefix) for prefix in backbone_prefixes[:backbone_layers]):
            if k not in hybrid_dict:  # 避免覆盖已复制的骨干层
                hybrid_dict[k] = v
    
    # 创建输出目录
    output_dir = Path('models')
    output_dir.mkdir(exist_ok=True)
    output_path = output_dir / args.output
    
    # 保存混合模型
    try:
        # 将混合权重加载到n模型中
        hybrid_model.model.load_state_dict(hybrid_dict)
        
        # 保存模型
        torch.save(hybrid_model.model.state_dict(), output_path)
        print(f"混合模型已保存到 {output_path}")
        
        # 创建训练配置文件
        config_path = 'scripts/train_config_hybrid.yaml'
        with open('scripts/train_config_s.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        config['model'] = str(output_path)
        config['name'] = 'pv_thermal_detection_hybrid'
        
        with open(config_path, 'w') as f:
            yaml.dump(config, f)
        
        print(f"混合模型训练配置已保存到 {config_path}")
        
        return True
    except Exception as e:
        print(f"创建混合模型时出错: {e}")
        return False

if __name__ == "__main__":
    create_hybrid_model() 