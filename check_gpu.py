import torch
import platform

print("系统信息:", platform.platform())
print("Python版本:", platform.python_version())
print("PyTorch版本:", torch.__version__)
print("CUDA可用:", torch.cuda.is_available())
print("CUDA版本:", torch.version.cuda if torch.cuda.is_available() else "无")
print("GPU数量:", torch.cuda.device_count())

if torch.cuda.device_count() > 0:
    print("GPU名称:", torch.cuda.get_device_name(0))
    print("GPU内存:", torch.cuda.get_device_properties(0).total_memory / 1024 / 1024 / 1024, "GB")

print("\n尝试创建CUDA张量...")
try:
    x = torch.tensor([1, 2, 3], device='cuda')
    print("成功创建CUDA张量:", x)
    print("当前设备:", x.device)
except Exception as e:
    print("创建CUDA张量失败:", e) 