import os
import sys
import argparse
import glob
import time
import shutil
from pathlib import Path
from ultralytics import YOLO

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='导出和部署模型')
    parser.add_argument('--model', type=str, default=None, help='要导出的模型文件路径')
    parser.add_argument('--format', type=str, default='onnx', 
                        choices=['onnx', 'torchscript', 'openvino', 'engine', 'coreml', 'saved_model'],
                        help='导出格式')
    parser.add_argument('--imgsz', type=int, default=416, help='导出模型的图像尺寸')
    parser.add_argument('--half', action='store_true', help='是否使用FP16精度(半精度)')
    parser.add_argument('--simplify', action='store_true', help='是否简化ONNX模型')
    return parser.parse_args()

def find_best_model(model_path=None):
    """找到最佳模型"""
    if model_path and os.path.exists(model_path):
        return model_path
    
    # 按优先级检查模型
    model_dirs = [
        "models/trained",
        "models",
        "runs/detect"
    ]
    
    # 先检查models/trained目录下的所有模型
    for model_dir in model_dirs:
        if os.path.exists(model_dir):
            model_files = glob.glob(os.path.join(model_dir, "*.pt"))
            if model_files:
                # 按修改时间排序，获取最新的模型
                latest_model = max(model_files, key=os.path.getmtime)
                print(f"找到最新模型: {latest_model}")
                return latest_model
    
    # 再检查所有训练运行目录
    if os.path.exists("runs/detect"):
        run_dirs = sorted(glob.glob("runs/detect/*/"), key=os.path.getmtime, reverse=True)
        for run_dir in run_dirs:
            best_model = os.path.join(run_dir, "weights/best.pt")
            if os.path.exists(best_model):
                print(f"找到训练结果模型: {best_model}")
                return best_model
    
    print("未找到任何模型!")
    return None

def export_model(model_path, format='onnx', imgsz=416, half=False, simplify=True):
    """导出模型为指定格式"""
    if not model_path or not os.path.exists(model_path):
        print(f"错误: 模型文件不存在 - {model_path}")
        return None
    
    print(f"\n正在导出模型: {model_path}")
    print(f"目标格式: {format}")
    print(f"图像尺寸: {imgsz}x{imgsz}")
    print(f"半精度: {'是' if half else '否'}")
    print(f"简化模型: {'是' if simplify else '否'}")
    
    try:
        # 加载模型
        model = YOLO(model_path)
        
        # 确定导出目录
        model_name = os.path.basename(model_path).replace('.pt', '')
        export_dir = f"models/exported/{model_name}_{format}"
        os.makedirs(export_dir, exist_ok=True)
        
        # 导出模型
        export_path = model.export(
            format=format,
            imgsz=imgsz,
            half=half,
            simplify=simplify,
            workspace=8,  # GB
            nms=True
        )
        
        # 复制导出的模型到导出目录
        if os.path.exists(export_path):
            target_path = os.path.join(export_dir, os.path.basename(export_path))
            shutil.copy2(export_path, target_path)
            print(f"\n模型导出成功:")
            print(f"- 源路径: {export_path}")
            print(f"- 目标路径: {target_path}")
            
            # 创建部署信息文件
            info_file = os.path.join(export_dir, 'deployment_info.txt')
            with open(info_file, 'w', encoding='utf-8') as f:
                f.write(f"模型: {model_name}\n")
                f.write(f"导出格式: {format}\n")
                f.write(f"导出时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"图像尺寸: {imgsz}x{imgsz}\n")
                f.write(f"半精度: {'是' if half else '否'}\n")
                f.write(f"简化模型: {'是' if simplify else '否'}\n")
                f.write("\n类别:\n")
                f.write("- 0: Diode anomaly (二极管异常)\n")
                f.write("- 1: Hot Spots (热点)\n")
                f.write("- 2: Reverse polarity (极性反转)\n")
                f.write("- 3: Vegetation (植被)\n")
                f.write("\n部署说明:\n")
                
                if format == 'onnx':
                    f.write("ONNX模型可以在多种深度学习框架中使用，如ONNX Runtime、OpenCV DNN等\n")
                    f.write("示例代码:\n")
                    f.write("```python\n")
                    f.write("import onnxruntime as ort\n")
                    f.write("import numpy as np\n")
                    f.write("import cv2\n\n")
                    f.write(f"# 加载模型\n")
                    f.write(f"session = ort.InferenceSession('{os.path.basename(target_path)}')\n")
                    f.write(f"# 准备输入\n")
                    f.write(f"img = cv2.imread('image.jpg')\n")
                    f.write(f"img = cv2.resize(img, ({imgsz}, {imgsz}))\n")
                    f.write(f"img = img.transpose(2, 0, 1)  # HWC -> CHW\n")
                    f.write(f"img = img.astype(np.float32) / 255.0\n")
                    f.write(f"img = np.expand_dims(img, 0)  # 添加batch维度\n\n")
                    f.write(f"# 运行推理\n")
                    f.write(f"input_name = session.get_inputs()[0].name\n")
                    f.write(f"output_names = [o.name for o in session.get_outputs()]\n")
                    f.write(f"outputs = session.run(output_names, {{input_name: img}})\n")
                    f.write("```\n")
                
                elif format == 'torchscript':
                    f.write("TorchScript模型可以在不需要Python环境的情况下使用PyTorch\n")
                    f.write("示例代码:\n")
                    f.write("```python\n")
                    f.write("import torch\n")
                    f.write("import cv2\n")
                    f.write("import numpy as np\n\n")
                    f.write(f"# 加载模型\n")
                    f.write(f"model = torch.jit.load('{os.path.basename(target_path)}')\n")
                    f.write(f"model.eval()\n\n")
                    f.write(f"# 准备输入\n")
                    f.write(f"img = cv2.imread('image.jpg')\n")
                    f.write(f"img = cv2.resize(img, ({imgsz}, {imgsz}))\n")
                    f.write(f"img = img.transpose(2, 0, 1)  # HWC -> CHW\n")
                    f.write(f"img = np.ascontiguousarray(img) / 255.0\n")
                    f.write(f"img = torch.from_numpy(img).float().unsqueeze(0)\n\n")
                    f.write(f"# 运行推理\n")
                    f.write(f"with torch.no_grad():\n")
                    f.write(f"    outputs = model(img)\n")
                    f.write("```\n")
                
                elif format == 'openvino':
                    f.write("OpenVINO模型优化用于Intel CPU和加速器\n")
                    f.write("示例代码:\n")
                    f.write("```python\n")
                    f.write("from openvino.runtime import Core\n")
                    f.write("import cv2\n")
                    f.write("import numpy as np\n\n")
                    f.write(f"# 加载模型\n")
                    f.write(f"ie = Core()\n")
                    f.write(f"model = ie.read_model(model='{os.path.basename(target_path)}')\n")
                    f.write(f"compiled_model = ie.compile_model(model=model, device_name='CPU')\n")
                    f.write(f"output_layer = compiled_model.output(0)\n\n")
                    f.write(f"# 准备输入\n")
                    f.write(f"img = cv2.imread('image.jpg')\n")
                    f.write(f"img = cv2.resize(img, ({imgsz}, {imgsz}))\n")
                    f.write(f"img = img.transpose(2, 0, 1)  # HWC -> CHW\n")
                    f.write(f"img = np.expand_dims(img, 0).astype(np.float32) / 255.0\n\n")
                    f.write(f"# 运行推理\n")
                    f.write(f"result = compiled_model([img])[output_layer]\n")
                    f.write("```\n")
            
            print(f"部署信息已保存到: {info_file}")
            return target_path
        else:
            print(f"警告: 导出模型文件不存在: {export_path}")
            return None
    
    except Exception as e:
        print(f"导出模型时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    args = parse_args()
    
    print("\n" + "="*80)
    print("光伏电板热成像故障检测 - 模型导出与部署")
    print("="*80)
    
    # 确保导出目录存在
    os.makedirs("models/exported", exist_ok=True)
    
    # 查找模型
    model_path = find_best_model(args.model)
    if not model_path:
        print("未找到有效的模型文件，无法导出")
        return
    
    # 导出模型
    export_model(
        model_path=model_path,
        format=args.format,
        imgsz=args.imgsz,
        half=args.half,
        simplify=args.simplify
    )
    
    print("\n导出完成!")

if __name__ == "__main__":
    main() 