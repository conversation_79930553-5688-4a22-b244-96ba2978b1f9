#!/usr/bin/env python3
"""
项目初始化脚本
用于设置项目环境、创建目录结构、验证配置等
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import create_directories, validate_config
from src.utils.logger import log_info, log_success, log_error

def create_init_files():
    """创建所有必要的__init__.py文件"""
    init_files = [
        "src/__init__.py",
        "src/data/__init__.py", 
        "src/models/__init__.py",
        "src/training/__init__.py",
        "src/inference/__init__.py",
        "src/utils/__init__.py",
        "src/web/__init__.py",
        "tests/__init__.py",
    ]
    
    for init_file in init_files:
        init_path = project_root / init_file
        init_path.parent.mkdir(parents=True, exist_ok=True)
        
        if not init_path.exists():
            with open(init_path, 'w', encoding='utf-8') as f:
                f.write(f'"""\n{init_path.parent.name}模块初始化文件\n"""\n')
            log_info(f"创建 {init_file}")

def create_placeholder_files():
    """创建占位符文件，确保目录结构完整"""
    placeholder_files = [
        "data/raw/.gitkeep",
        "data/processed/.gitkeep", 
        "data/external/.gitkeep",
        "models/pretrained/.gitkeep",
        "models/trained/.gitkeep",
        "models/exported/.gitkeep",
        "outputs/logs/.gitkeep",
        "outputs/results/.gitkeep",
        "outputs/reports/.gitkeep",
        "notebooks/.gitkeep",
        "docs/.gitkeep",
    ]
    
    for placeholder in placeholder_files:
        placeholder_path = project_root / placeholder
        placeholder_path.parent.mkdir(parents=True, exist_ok=True)
        
        if not placeholder_path.exists():
            with open(placeholder_path, 'w', encoding='utf-8') as f:
                f.write("# 此文件用于保持目录结构\n")
            log_info(f"创建占位符 {placeholder}")

def create_gitignore():
    """创建.gitignore文件"""
    gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# Data files
data/raw/*
!data/raw/.gitkeep
data/processed/*
!data/processed/.gitkeep

# Model files
models/pretrained/*.pt
models/trained/*.pt
models/exported/*
!models/*/.gitkeep

# Output files
outputs/logs/*
outputs/results/*
outputs/reports/*
!outputs/*/.gitkeep

# OS
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log

# Environment variables
.env
.env.local
"""
    
    gitignore_path = project_root / ".gitignore"
    if not gitignore_path.exists():
        with open(gitignore_path, 'w', encoding='utf-8') as f:
            f.write(gitignore_content)
        log_info("创建 .gitignore 文件")

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = [
        'torch',
        'ultralytics', 
        'opencv-python',
        'numpy',
        'matplotlib',
        'loguru'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            log_info(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            log_error(f"❌ {package} 未安装")
    
    if missing_packages:
        log_error(f"缺少以下依赖包: {', '.join(missing_packages)}")
        log_info("请运行: pip install -r requirements.txt")
        return False
    else:
        log_success("所有依赖包检查通过")
        return True

def main():
    """主函数"""
    print("🚀 开始初始化光伏故障检测项目...")
    
    try:
        # 1. 创建目录结构
        log_info("步骤1: 创建目录结构")
        create_directories()
        
        # 2. 创建__init__.py文件
        log_info("步骤2: 创建模块初始化文件")
        create_init_files()
        
        # 3. 创建占位符文件
        log_info("步骤3: 创建占位符文件")
        create_placeholder_files()
        
        # 4. 创建.gitignore
        log_info("步骤4: 创建Git忽略文件")
        create_gitignore()
        
        # 5. 验证配置
        log_info("步骤5: 验证项目配置")
        validate_config()
        
        # 6. 检查依赖
        log_info("步骤6: 检查依赖包")
        deps_ok = check_dependencies()
        
        if deps_ok:
            log_success("🎉 项目初始化完成！")
            print("\n" + "="*60)
            print("📁 项目结构已创建完成")
            print("📋 下一步操作:")
            print("   1. 安装依赖: pip install -r requirements.txt")
            print("   2. 下载数据: python scripts/download_data.py")
            print("   3. 开始训练: python scripts/train.py")
            print("="*60)
        else:
            log_error("❌ 项目初始化失败，请先安装依赖包")
            
    except Exception as e:
        log_error(f"初始化过程中发生错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
