batch: 2
data: data/pv_thermal_detection/data.yaml
degrees: 0.3  # 增加旋转角度
deterministic: true
device: 0
epochs: 100  # 大幅增加训练轮数
exist_ok: false
fliplr: 0.5
flipud: 0.1  # 增加上下翻转概率
hsv_h: 0.015
hsv_s: 0.7
hsv_v: 0.4
imgsz: 384  # 适中图像尺寸，平衡内存和精度
lr0: 0.001  # 更低的初始学习率
lrf: 0.01
mixup: 0.1  # 启用mixup增强
model: yolov8s.pt  # 使用YOLOv8s模型，提高精度
momentum: 0.937
mosaic: 1.0
name: pv_anomaly_yolov8s_enhanced_v1
patience: 50  # 增加早停耐心值
pretrained: true
project: runs/detect
save: true
save_period: 10
scale: 0.5
seed: 0
translate: 0.1
verbose: true
warmup_bias_lr: 0.1
warmup_epochs: 5.0  # 增加预热轮数
warmup_momentum: 0.8
weight_decay: 0.0005
workers: 2

# 高级优化选项
single_cls: false  # 是否当作单类问题处理
rect: false  # 矩形训练，对于大小不一的图像可提高效率
cos_lr: true  # 余弦学习率调度，通常效果更好
close_mosaic: 10  # 最后10个epoch关闭mosaic增强，提高精度
copy_paste: 0.1  # 启用复制粘贴增强
perspective: 0.0003  # 添加透视变换
shear: 0.2  # 添加剪切变换
auto_augment: randaugment  # 使用RandAugment自动增强
erasing: 0.4  # 随机擦除增强

# 类别权重，解决类别不平衡问题
cls_weights: [1.0, 1.0, 2.0, 1.5]  # 为稀有类增加权重 