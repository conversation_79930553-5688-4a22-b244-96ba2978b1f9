# 光伏电板故障检测系统 - 重构版

基于YOLOv8的光伏电板热成像故障检测系统，采用分层检测架构实现高效准确的故障识别。

## 🎯 项目目标

检测光伏电板的四种主要故障类型：
- 二极管异常 (Diode anomaly)
- 热点 (Hot Spots)
- 反向极性 (Reverse polarity)
- 植被遮挡 (Vegetation)

## 📁 项目结构

```
pv_detection_clean/
├── README.md                    # 项目说明文档
├── requirements.txt             # 依赖包列表
├── config/                      # 配置文件目录
│   ├── __init__.py
│   ├── settings.py             # 全局配置
│   └── model_configs/          # 模型配置文件
├── src/                        # 源代码目录
│   ├── __init__.py
│   ├── data/                   # 数据处理模块
│   │   ├── __init__.py
│   │   ├── dataset.py          # 数据集类
│   │   ├── transforms.py       # 数据变换
│   │   └── utils.py            # 数据工具函数
│   ├── models/                 # 模型相关模块
│   │   ├── __init__.py
│   │   ├── yolo_wrapper.py     # YOLO模型封装
│   │   ├── hierarchical.py     # 分层检测模型
│   │   └── utils.py            # 模型工具函数
│   ├── training/               # 训练相关模块
│   │   ├── __init__.py
│   │   ├── trainer.py          # 训练器
│   │   ├── validator.py        # 验证器
│   │   └── callbacks.py        # 训练回调
│   ├── inference/              # 推理相关模块
│   │   ├── __init__.py
│   │   ├── detector.py         # 检测器
│   │   └── postprocess.py      # 后处理
│   ├── utils/                  # 通用工具模块
│   │   ├── __init__.py
│   │   ├── logger.py           # 日志工具
│   │   ├── metrics.py          # 评估指标
│   │   └── visualization.py    # 可视化工具
│   └── web/                    # Web界面模块
│       ├── __init__.py
│       ├── app.py              # Flask应用
│       └── templates/          # HTML模板
├── data/                       # 数据目录
│   ├── raw/                    # 原始数据
│   ├── processed/              # 处理后数据
│   └── external/               # 外部数据
├── models/                     # 模型文件目录
│   ├── pretrained/             # 预训练模型
│   ├── trained/                # 训练好的模型
│   └── exported/               # 导出的模型
├── notebooks/                  # Jupyter笔记本
├── scripts/                    # 脚本目录
│   ├── download_data.py        # 数据下载脚本
│   ├── train.py                # 训练脚本
│   ├── evaluate.py             # 评估脚本
│   └── deploy.py               # 部署脚本
├── tests/                      # 测试目录
├── docs/                       # 文档目录
└── outputs/                    # 输出结果目录
    ├── logs/                   # 日志文件
    ├── results/                # 检测结果
    └── reports/                # 分析报告
```

## 🚀 快速开始

### 环境准备
```bash
# 创建虚拟环境
conda create -n pv_detection python=3.9 -y
conda activate pv_detection

# 安装依赖
pip install -r requirements.txt
```

### 数据准备
```bash
# 下载数据集
python scripts/download_data.py
```

### 模型训练
```bash
# 基础训练
python scripts/train.py --config config/model_configs/yolov8n.yaml

# 分层模型训练
python scripts/train.py --config config/model_configs/hierarchical.yaml
```

### 模型评估
```bash
# 评估模型性能
python scripts/evaluate.py --model models/trained/best.pt
```

## 📚 学习路径

本项目按照以下阶段逐步实现，每个阶段都有详细的原理讲解：

1. **阶段1**: 项目基础架构搭建 ✅
2. **阶段2**: 数据处理模块实现
3. **阶段3**: 基础模型训练实现
4. **阶段4**: 模型评估与可视化
5. **阶段5**: 推理与检测实现
6. **阶段6**: 分层检测架构实现
7. **阶段7**: 模型部署与优化
8. **阶段8**: Web界面与系统集成

## 🔧 技术栈

- **深度学习**: PyTorch, Ultralytics YOLOv8
- **计算机视觉**: OpenCV, PIL
- **数据处理**: NumPy, Pandas
- **可视化**: Matplotlib, Seaborn
- **Web框架**: Flask
- **部署**: ONNX, Docker

## 📖 原理说明

详细的原理讲解请参考 `docs/` 目录下的文档。

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
