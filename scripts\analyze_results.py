import os
import argparse
import numpy as np
import matplotlib.pyplot as plt
import yaml
from pathlib import Path
import cv2
from ultralytics import YOLO
import seaborn as sns
from sklearn.metrics import confusion_matrix
import sys
import pandas as pd
import shutil
import torch
import traceback

def parse_args():
    parser = argparse.ArgumentParser(description='分析YOLOv8训练结果')
    parser.add_argument('--results', type=str, default='runs/detect/pv_anomaly_yolov8s4', 
                        help='训练结果目录')
    parser.add_argument('--model', type=str, default='models/trained/pv_anomaly_yolov8s.pt',
                        help='模型文件路径')
    parser.add_argument('--data', type=str, default='data/pv_thermal_detection/data.yaml',
                        help='数据集配置文件')
    parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--iou', type=float, default=0.45, help='IoU阈值')
    parser.add_argument('--max-samples', type=int, default=10, help='每个类别最多显示的样本数')
    parser.add_argument('--csv', type=str, help='CSV结果文件路径', default='')
    parser.add_argument('--output-dir', type=str, default='models/analysis/pv_anomaly_yolov8s', help='分析结果输出目录')
    parser.add_argument('--cpu-only', action='store_true', help='只使用CPU进行分析')
    parser.add_argument('--batch-size', type=int, default=1, help='评估批次大小')
    parser.add_argument('--num-threads', type=int, default=1, help='GPU线程数')
    return parser.parse_args()

def plot_training_curves(results_csv, output_dir):
    """绘制训练曲线"""
    if not os.path.exists(results_csv):
        print(f"未找到训练结果CSV文件: {results_csv}")
        return None
    
    try:
        # 读取结果文件
        results_df = pd.read_csv(results_csv)
        
        # 创建图表
        fig, axs = plt.subplots(2, 2, figsize=(15, 10))
        
        # 绘制损失曲线
        epochs = results_df['epoch'].values
        train_box_loss = results_df['train/box_loss'].values
        train_cls_loss = results_df['train/cls_loss'].values
        train_dfl_loss = results_df['train/dfl_loss'].values
        
        axs[0, 0].plot(epochs, train_box_loss, label='box损失')
        axs[0, 0].plot(epochs, train_cls_loss, label='分类损失')
        axs[0, 0].plot(epochs, train_dfl_loss, label='DFL损失')
        axs[0, 0].set_xlabel('轮数')
        axs[0, 0].set_ylabel('损失')
        axs[0, 0].set_title('训练损失')
        axs[0, 0].legend()
        axs[0, 0].grid(True)
        
        # 绘制mAP@0.5曲线
        mAP50 = results_df['metrics/mAP50(B)'].values
        axs[0, 1].plot(epochs, mAP50, label='mAP@0.5', color='green')
        axs[0, 1].set_xlabel('轮数')
        axs[0, 1].set_ylabel('mAP@0.5')
        axs[0, 1].set_title('模型mAP@0.5性能')
        axs[0, 1].legend()
        axs[0, 1].grid(True)
        
        # 绘制mAP@0.5:0.95曲线
        mAP50_95 = results_df['metrics/mAP50-95(B)'].values
        axs[1, 0].plot(epochs, mAP50_95, label='mAP@0.5:0.95', color='purple')
        axs[1, 0].set_xlabel('轮数')
        axs[1, 0].set_ylabel('mAP@0.5:0.95')
        axs[1, 0].set_title('模型mAP@0.5:0.95性能')
        axs[1, 0].legend()
        axs[1, 0].grid(True)
        
        # 绘制精确率和召回率
        precision = results_df['metrics/precision(B)'].values
        recall = results_df['metrics/recall(B)'].values
        axs[1, 1].plot(epochs, precision, label='精确率', color='red')
        axs[1, 1].plot(epochs, recall, label='召回率', color='blue')
        axs[1, 1].set_xlabel('轮数')
        axs[1, 1].set_ylabel('值')
        axs[1, 1].set_title('精确率和召回率')
        axs[1, 1].legend()
        axs[1, 1].grid(True)
        
        # 保存并显示图表
        plt.tight_layout()
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, 'training_curves.png')
        plt.savefig(output_path)
        plt.close(fig)
        
        print(f"训练曲线已保存到 {output_path}")
        return output_path
    except Exception as e:
        print(f"绘制训练曲线时出错: {e}")
        traceback.print_exc()
        return None

def analyze_model_performance(model_path, data_yaml, output_dir, conf_thresh=0.25, iou_thresh=0.45, cpu_only=False, batch_size=1, num_threads=1):
    """分析模型性能"""
    if not os.path.exists(model_path):
        print(f"错误: 模型文件不存在: {model_path}")
        return False
    
    try:
        # 控制CUDA线程数量和内存使用
        if torch.cuda.is_available() and not cpu_only:
            print(f"使用GPU进行模型分析，线程数: {num_threads}")
            torch.set_num_threads(num_threads)
            # 限制可用CUDA设备
            os.environ["CUDA_VISIBLE_DEVICES"] = "0"
            # 启用内存自动释放
            torch.cuda.empty_cache()
        else:
            print("使用CPU进行模型分析")
            os.environ["CUDA_VISIBLE_DEVICES"] = ""
        
        # 加载模型
        model = YOLO(model_path)
        
        # 加载数据集配置
        with open(data_yaml, 'r') as f:
            data_config = yaml.safe_load(f)
        
        class_names = data_config.get('names', [])
        
        # 运行验证
        print(f"\n使用置信度阈值 {conf_thresh} 和 IoU阈值 {iou_thresh} 评估模型...")
        val_results = model.val(data=data_yaml, conf=conf_thresh, iou=iou_thresh)
        
        # 打印总体性能
        print("\n总体性能:")
        print(f"mAP@0.5: {val_results.box.map50:.4f}")
        print(f"mAP@0.5:0.95: {val_results.box.map:.4f}")
        print(f"精确率 (Precision): {val_results.box.mp:.4f}")
        print(f"召回率 (Recall): {val_results.box.mr:.4f}")
        
        # 创建性能报告文件
        report_path = os.path.join(output_dir, 'model_performance.txt')
        with open(report_path, 'w') as f:
            f.write("# YOLOv8s 光伏异常检测模型性能评估\n\n")
            
            f.write("## 总体性能\n")
            f.write(f"mAP@0.5: {val_results.box.map50:.4f}\n")
            f.write(f"mAP@0.5:0.95: {val_results.box.map:.4f}\n")
            f.write(f"精确率 (Precision): {val_results.box.mp:.4f}\n")
            f.write(f"召回率 (Recall): {val_results.box.mr:.4f}\n\n")
            
            f.write("## 各类别性能\n")
            for i, name in enumerate(class_names):
                f.write(f"\n类别 {i}: {name}\n")
                f.write(f"mAP@0.5: {val_results.box.maps50[i]:.4f}\n")
                f.write(f"精确率: {val_results.box.mps[i]:.4f}\n")
                f.write(f"召回率: {val_results.box.mrs[i]:.4f}\n")
        
        print(f"性能报告已保存至: {report_path}")
        
        # 创建性能可视化图表
        plt.figure(figsize=(12, 6))
        
        # 绘制每个类别的mAP, Precision, Recall
        x = np.arange(len(class_names))
        width = 0.25
        
        plt.bar(x - width, val_results.box.maps50, width, label='mAP@0.5')
        plt.bar(x, val_results.box.mps, width, label='Precision')
        plt.bar(x + width, val_results.box.mrs, width, label='Recall')
        
        plt.xlabel('类别')
        plt.ylabel('性能')
        plt.title('各类别检测性能')
        plt.xticks(x, class_names, rotation=45, ha='right')
        plt.ylim(0, 1.0)
        plt.legend()
        plt.tight_layout()
        
        # 保存图表
        output_path = os.path.join(output_dir, 'class_performance.png')
        plt.savefig(output_path)
        plt.close()
        
        print(f"类别性能可视化已保存到 {output_path}")
        return val_results
    except Exception as e:
        print(f"分析模型性能时出错: {e}")
        traceback.print_exc()
        return None
    finally:
        # 清理内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

def analyze_training_results(results_csv, output_dir):
    """分析训练结果并生成摘要报告"""
    if not os.path.exists(results_csv):
        print(f"错误: 结果文件不存在: {results_csv}")
        return False
    
    try:
        # 读取CSV结果文件
        results = pd.read_csv(results_csv)
        
        # 输出文件路径
        os.makedirs(output_dir, exist_ok=True)
        output_txt = os.path.join(output_dir, 'training_summary.txt')
        
        print(f"正在分析训练结果: {results_csv}")
        print(f"总共训练了 {len(results)} 个epoch")
        
        # 获取关键指标
        final_epoch = results.iloc[-1]
        best_map = results['metrics/mAP50(B)'].max()
        best_map_epoch = results['metrics/mAP50(B)'].idxmax() + 1  # +1因为epoch从1开始
        
        best_map50_95 = results['metrics/mAP50-95(B)'].max()
        best_map50_95_epoch = results['metrics/mAP50-95(B)'].idxmax() + 1
        
        # 创建摘要报告
        with open(output_txt, 'w') as f:
            f.write("# 光伏异常检测YOLOv8s模型训练结果\n\n")
            
            f.write("## 训练摘要\n")
            f.write(f"总训练轮次: {len(results)}\n")
            f.write(f"最佳mAP50: {best_map:.4f} (第{best_map_epoch}轮)\n")
            f.write(f"最佳mAP50-95: {best_map50_95:.4f} (第{best_map50_95_epoch}轮)\n\n")
            
            f.write("## 最终轮次性能\n")
            f.write(f"mAP50: {final_epoch['metrics/mAP50(B)']:.4f}\n")
            f.write(f"mAP50-95: {final_epoch['metrics/mAP50-95(B)']:.4f}\n")
            f.write(f"精度(Precision): {final_epoch['metrics/precision(B)']:.4f}\n")
            f.write(f"召回率(Recall): {final_epoch['metrics/recall(B)']:.4f}\n\n")
            
            f.write("## 每10个轮次的性能\n")
            f.write("轮次\tmAP50\tmAP50-95\t精度\t召回率\n")
            for i in range(0, len(results), 10):
                if i < len(results):
                    epoch = results.iloc[i]
                    f.write(f"{i+1}\t{epoch['metrics/mAP50(B)']:.4f}\t{epoch['metrics/mAP50-95(B)']:.4f}\t{epoch['metrics/precision(B)']:.4f}\t{epoch['metrics/recall(B)']:.4f}\n")
        
        print(f"训练分析摘要已保存至: {output_txt}")
        return True
    except Exception as e:
        print(f"分析结果时出错: {e}")
        traceback.print_exc()
        return False

def copy_visualization_files(source_dir, target_dir):
    """复制训练生成的可视化文件到目标目录"""
    try:
        source_path = Path(source_dir)
        os.makedirs(target_dir, exist_ok=True)
        
        # 查找可视化图像
        visualization_files = [
            source_path / 'labels.jpg',
            source_path / 'labels_correlogram.jpg',
            source_path / 'train_batch0.jpg',
            source_path / 'train_batch1.jpg',
            source_path / 'train_batch2.jpg'
        ]
        
        copied_count = 0
        for file_path in visualization_files:
            if file_path.exists():
                target_path = os.path.join(target_dir, file_path.name)
                shutil.copy(file_path, target_path)
                print(f"复制 {file_path} -> {target_path}")
                copied_count += 1
        
        # 查找结果文件
        results_files = list(source_path.glob('results.*'))
        for file_path in results_files:
            target_path = os.path.join(target_dir, file_path.name)
            shutil.copy(file_path, target_path)
            print(f"复制 {file_path} -> {target_path}")
            copied_count += 1
        
        # 复制混淆矩阵等文件
        confusion_files = list(source_path.glob('confusion_matrix*'))
        for file_path in confusion_files:
            target_path = os.path.join(target_dir, file_path.name)
            shutil.copy(file_path, target_path)
            print(f"复制 {file_path} -> {target_path}")
            copied_count += 1
        
        # 复制PR曲线等文件
        pr_files = list(source_path.glob('PR_curve*'))
        for file_path in pr_files:
            target_path = os.path.join(target_dir, file_path.name)
            shutil.copy(file_path, target_path)
            print(f"复制 {file_path} -> {target_path}")
            copied_count += 1
        
        # 复制F1曲线等文件
        f1_files = list(source_path.glob('F1_curve*'))
        for file_path in f1_files:
            target_path = os.path.join(target_dir, file_path.name)
            shutil.copy(file_path, target_path)
            print(f"复制 {file_path} -> {target_path}")
            copied_count += 1
        
        if copied_count > 0:
            print(f"已复制 {copied_count} 个可视化文件到 {target_dir}")
        else:
            print(f"未找到可复制的可视化文件")
        
        return copied_count > 0
    except Exception as e:
        print(f"复制可视化文件时出错: {e}")
        traceback.print_exc()
        return False

def main():
    # 解析命令行参数
    args = parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    print(f"分析结果将保存到: {args.output_dir}")
    
    # 确定CSV结果文件路径
    if args.csv:
        results_csv = args.csv
    else:
        # 尝试从results目录查找
        results_csv = os.path.join(args.results, 'results.csv')
    
    if not os.path.exists(results_csv):
        print(f"未找到训练结果CSV文件: {results_csv}")
        results_csv = None
    else:
        print(f"使用训练结果文件: {results_csv}")
        
        # 分析训练结果并生成摘要
        analyze_training_results(results_csv, args.output_dir)
        
        # 绘制训练曲线
        plot_training_curves(results_csv, args.output_dir)
    
    # 分析模型性能
    if os.path.exists(args.model):
        print(f"分析模型性能: {args.model}")
        analyze_model_performance(args.model, args.data, args.output_dir, 
                                conf_thresh=args.conf, iou_thresh=args.iou, 
                                cpu_only=args.cpu_only, batch_size=args.batch_size, num_threads=args.num_threads)
    else:
        print(f"跳过模型性能分析: 未找到模型文件 {args.model}")
    
    # 复制训练过程中生成的可视化文件
    copy_visualization_files(args.results, args.output_dir)
    
    print(f"\n分析完成！结果已保存到: {args.output_dir}")

if __name__ == "__main__":
    main() 