import os
import yaml
from pathlib import Path

def check_dataset(yaml_path="data/pv_thermal_detection/data.yaml"):
    """检查数据集配置和路径"""
    print(f"\n检查数据集配置文件: {yaml_path}")
    
    # 验证yaml文件存在
    if not os.path.exists(yaml_path):
        print(f"错误: 数据集配置文件不存在: {yaml_path}")
        return False
    
    # 读取配置
    try:
        with open(yaml_path, 'r') as f:
            config = yaml.safe_load(f)
            
        # 显示基本信息
        print("\n数据集基本信息:")
        nc = config.get('nc', 0)
        print(f"类别数量: {nc}")
        
        names = config.get('names', [])
        print(f"类别名称: {names}")
        
        if len(names) != nc:
            print(f"警告: 类别数量 ({nc}) 与类别名称列表长度 ({len(names)}) 不一致")
        
        # 检查路径
        yaml_dir = os.path.dirname(os.path.abspath(yaml_path))
        print(f"\n数据集配置文件所在目录: {yaml_dir}")
        
        # 检查关键路径
        for key in ['train', 'val', 'test']:
            if key in config:
                path_str = config[key]
                print(f"\n检查 {key} 路径: {path_str}")
                
                # 尝试不同的路径解析方式
                possible_paths = [
                    # 相对于yaml文件的路径
                    os.path.join(yaml_dir, path_str),
                    # 相对于工作目录的路径
                    os.path.join(os.getcwd(), path_str),
                    # 原始路径（如果是绝对路径）
                    path_str
                ]
                
                found = False
                valid_path = None
                for test_path in possible_paths:
                    if os.path.exists(test_path):
                        found = True
                        valid_path = test_path
                        break
                
                if found:
                    print(f"✓ 路径有效: {valid_path}")
                    # 统计图像
                    image_files = [f for f in os.listdir(valid_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                    print(f"  图像文件数量: {len(image_files)}")
                    if image_files:
                        print(f"  示例图像: {', '.join(image_files[:3])}...")
                        
                    # 检查对应的标签目录
                    labels_path = valid_path.replace('images', 'labels')
                    if os.path.exists(labels_path):
                        label_files = [f for f in os.listdir(labels_path) if f.lower().endswith('.txt')]
                        print(f"  标签文件数量: {len(label_files)}")
                        if len(label_files) != len(image_files):
                            print(f"  警告: 图像文件数量 ({len(image_files)}) 与标签文件数量 ({len(label_files)}) 不匹配")
                    else:
                        print(f"  警告: 标签目录不存在: {labels_path}")
                else:
                    print(f"✗ 无效路径: 尝试了以下路径但都不存在:")
                    for i, p in enumerate(possible_paths):
                        print(f"  {i+1}. {p}")
        
        return True
    except Exception as e:
        print(f"检查数据集时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_dataset() 