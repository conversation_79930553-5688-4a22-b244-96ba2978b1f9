# 光伏电板故障检测模型参数优化指南

本文档提供了关于如何根据训练结果优化YOLOv8模型参数的建议，以提高检测性能。

## 常见问题与优化建议

### 1. 模型欠拟合（训练集和验证集都有较高误差）

如果模型无法很好地拟合训练数据：

- **增加模型容量**：使用更大的模型
  ```yaml
  model: yolov8s.pt  # 小型模型（默认是yolov8n.pt最小型）
  # 或者更大的模型
  model: yolov8m.pt  # 中型模型
  model: yolov8l.pt  # 大型模型
  ```

- **增加训练轮数**：
  ```yaml
  epochs: 200  # 默认是100
  ```

- **调整学习率**：
  ```yaml
  lr0: 0.01  # 初始学习率（可尝试0.005-0.02的范围）
  ```

### 2. 模型过拟合（训练集误差低，验证集误差高）

如果模型在训练集上表现良好但在验证集上表现不佳：

- **增加数据增强**：
  ```yaml
  # 增强色调、饱和度和亮度变化
  hsv_h: 0.015  # 色调增强
  hsv_s: 0.7    # 饱和度增强
  hsv_v: 0.4    # 亮度增强
  
  # 增加旋转、平移和缩放
  degrees: 10.0  # 旋转增强角度
  translate: 0.2  # 平移比例
  scale: 0.5     # 缩放比例
  ```

- **添加混合增强**：
  ```yaml
  mixup: 0.1  # 启用mixup增强（0-1之间）
  ```

- **添加权重衰减**：
  ```yaml
  weight_decay: 0.001  # 增加权重衰减（默认0.0005）
  ```

- **早停设置**：
  ```yaml
  patience: 30  # 提高早停轮数（默认20），避免过早停止
  ```

### 3. 检测精度问题

如果特定类别的检测精度不高：

- **调整置信度阈值**：在测试时可以调整置信度阈值
  ```bash
  python scripts/analyze_results.py --conf 0.3  # 默认是0.25
  ```

- **增加类别特定的权重**：如果某些类别样本较少
  ```yaml
  # 在train_model.py中添加类别权重
  class_weights: [1.0, 2.0, 1.5, 1.0]  # 第二和第三个类别权重更高
  ```

### 4. 小目标检测不佳

如果模型对小目标的检测效果不佳：

- **增加图像尺寸**：
  ```yaml
  imgsz: 800  # 默认是640
  ```

- **使用多尺度训练**：
  ```yaml
  multi_scale: True
  ```

### 5. 训练速度优化

如果训练速度较慢：

- **减小批量大小**：
  ```yaml
  batch: 8  # 默认是16
  ```

- **减小图像尺寸**：
  ```yaml
  imgsz: 416  # 较小的图像尺寸
  ```

- **使用CPU模式**（如果没有适合的GPU）：
  ```bash
  python scripts/train_model.py --device cpu
  ```

## 根据训练结果判断下一步优化方向

1. **观察损失曲线**：
   - 如果训练损失和验证损失都很高且平稳 → 模型欠拟合，增加模型复杂度
   - 如果训练损失持续下降而验证损失上升 → 模型过拟合，增加正则化

2. **观察mAP指标**：
   - 如果mAP@0.5很高但mAP@0.5:0.95较低 → 检测框定位不够精确，调整目标框回归损失权重
   - 如果mAP很低 → 基础检测能力不足，考虑更大的模型或更多训练轮数

3. **观察类别性能**：
   - 如果某些类别性能特别差 → 为这些类别增加样本或调整类别权重
   - 如果所有类别性能都不佳 → 考虑基础模型能力不足或数据质量问题

## 超参数推荐值

| 参数 | 小数据集 | 中等数据集 | 大数据集 |
|------|---------|-----------|---------|
| 模型 | yolov8n | yolov8s/m | yolov8l/x |
| epochs | 50-100 | 100-200 | 200-300 |
| batch | 8-16 | 16-32 | 32-64 |
| imgsz | 640 | 640-800 | 800-1024 |
| lr0 | 0.01 | 0.01 | 0.01 |
| weight_decay | 0.0005-0.001 | 0.0005 | 0.0005 |

## 实用技巧

1. **恢复训练**：如果训练中断，可以使用`--resume`参数恢复训练
   ```bash
   python scripts/train_model.py --resume
   ```

2. **使用预训练模型**：始终使用预训练模型，可以显著加快收敛速度
   ```yaml
   pretrained: True  # 默认为True
   ```

3. **跳过部分分析**：如果仅需训练不需分析
   ```bash
   python scripts/run_pipeline.py --skip-analyze
   ```

4. **尝试不同的优化器**：
   ```yaml
   optimizer: 'SGD'  # 或 'Adam', 'AdamW'
   ```

祝您训练顺利！ 