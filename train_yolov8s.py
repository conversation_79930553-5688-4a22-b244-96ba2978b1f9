import os
import sys
import subprocess
import torch
import psutil
import gc
import time
import argparse

def parse_args():
    parser = argparse.ArgumentParser(description='训练YOLOv8s模型')
    parser.add_argument('--config', type=str, default='scripts/train_config_s.yaml', help='训练配置文件')
    return parser.parse_args()

# 检查是否有其他Python进程正在运行
def check_running_python_processes():
    python_procs = [p for p in psutil.process_iter(['pid', 'name']) 
                  if 'python' in p.info['name'].lower()]
    
    if len(python_procs) > 1:  # 1是当前进程
        print(f"警告: 发现 {len(python_procs)-1} 个其他Python进程正在运行")
        for proc in python_procs:
            if proc.pid != os.getpid():
                print(f"PID: {proc.pid}, 名称: {proc.info['name']}")
        
        response = input("是否终止这些进程? (y/n): ")
        if response.lower() == 'y':
            for proc in python_procs:
                if proc.pid != os.getpid():
                    try:
                        proc.kill()
                        print(f"已终止进程 {proc.pid}")
                    except:
                        print(f"无法终止进程 {proc.pid}")
            print("已终止所有其他Python进程")

# 检查系统内存
def check_system_memory():
    vm = psutil.virtual_memory()
    print(f"系统内存: 总计={vm.total/(1024**3):.2f}GB, 可用={vm.available/(1024**3):.2f}GB, 使用率={vm.percent}%")
    
    if vm.percent > 90:
        print("警告: 系统内存使用率超过90%，训练可能会受到影响")
        return False
    return True

# 手动释放内存
def free_memory():
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    print("已手动清理内存")

def main():
    start_time = time.time()
    args = parse_args()
    
    print("="*80)
    print(f"开始训练YOLOv8s模型")
    print("="*80)
    
    # 运行前检查
    check_running_python_processes()
    check_system_memory()
    free_memory()

    # 设置环境变量限制CUDA内存使用
    os.environ['CUDA_VISIBLE_DEVICES'] = '0'
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128,garbage_collection_threshold:0.6'

    # 检查CUDA
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print(f"✓ CUDA available, device: {torch.cuda.get_device_name(0)}")
        print(f"✓ Total GPU memory: {torch.cuda.get_device_properties(0).total_memory / (1024**3):.2f} GB")
    else:
        print("✗ CUDA not available")
        cuda_choice = input("CUDA不可用。是否使用CPU继续训练? (y/n): ")
        if cuda_choice.lower() != 'y':
            sys.exit(1)

    # 启动训练
    print("\n启动YOLOv8s模型训练...")
    train_script = "scripts/start_training.py"
    train_config = args.config
    
    try:
        cmd = ["python", train_script, "--cfg", train_config]
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=False)
        
        if result.returncode != 0:
            print(f"\n训练失败，返回代码: {result.returncode}")
        else:
            print("\n训练完成！")
            
            # 训练完成后分析结果
            print("\n分析训练结果...")
            analysis_cmd = ["python", "scripts/analyze_results.py", 
                          "--results", "runs/detect/pv_thermal_detection_s", 
                          "--data", "data/pv_thermal_detection/data.yaml", 
                          "--conf", "0.25", 
                          "--max-samples", "10"]
            
            subprocess.run(analysis_cmd, check=False)
            
    except KeyboardInterrupt:
        print("\n训练被用户中断")
    except Exception as e:
        print(f"\n训练过程中发生错误: {e}")
    finally:
        # 确保在退出时释放内存
        free_memory()
        
        # 计算总用时
        end_time = time.time()
        duration = end_time - start_time
        hours, remainder = divmod(duration, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        print("="*80)
        print(f"YOLOv8s模型训练完成")
        print(f"总用时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
        print("="*80)

if __name__ == "__main__":
    main() 