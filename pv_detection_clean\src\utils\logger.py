"""
日志工具模块
提供统一的日志记录功能
"""

import sys
from pathlib import Path
from loguru import logger
from config.settings import LogConfig

class Logger:
    """日志管理器"""
    
    def __init__(self, name: str = "PV_Detection"):
        self.name = name
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志配置"""
        # 移除默认的控制台输出
        logger.remove()
        
        # 添加控制台输出
        logger.add(
            sys.stdout,
            format=LogConfig.LOG_FORMAT,
            level=LogConfig.LOG_LEVEL,
            colorize=True
        )
        
        # 确保日志目录存在
        LogConfig.LOG_DIR.mkdir(parents=True, exist_ok=True)
        
        # 添加文件输出
        logger.add(
            LogConfig.LOG_DIR / f"{self.name}.log",
            format=LogConfig.LOG_FORMAT,
            level=LogConfig.LOG_LEVEL,
            rotation=LogConfig.LOG_ROTATION,
            retention=LogConfig.LOG_RETENTION,
            encoding="utf-8"
        )
        
        # 添加错误日志文件
        logger.add(
            LogConfig.LOG_DIR / f"{self.name}_error.log",
            format=LogConfig.LOG_FORMAT,
            level="ERROR",
            rotation=LogConfig.LOG_ROTATION,
            retention=LogConfig.LOG_RETENTION,
            encoding="utf-8"
        )
    
    def info(self, message: str):
        """记录信息日志"""
        logger.info(f"[{self.name}] {message}")
    
    def debug(self, message: str):
        """记录调试日志"""
        logger.debug(f"[{self.name}] {message}")
    
    def warning(self, message: str):
        """记录警告日志"""
        logger.warning(f"[{self.name}] {message}")
    
    def error(self, message: str):
        """记录错误日志"""
        logger.error(f"[{self.name}] {message}")
    
    def critical(self, message: str):
        """记录严重错误日志"""
        logger.critical(f"[{self.name}] {message}")
    
    def success(self, message: str):
        """记录成功日志"""
        logger.success(f"[{self.name}] {message}")

# 创建全局日志实例
main_logger = Logger("Main")
data_logger = Logger("Data")
model_logger = Logger("Model")
training_logger = Logger("Training")
inference_logger = Logger("Inference")

# 便捷函数
def log_info(message: str, module: str = "Main"):
    """记录信息日志的便捷函数"""
    if module == "Data":
        data_logger.info(message)
    elif module == "Model":
        model_logger.info(message)
    elif module == "Training":
        training_logger.info(message)
    elif module == "Inference":
        inference_logger.info(message)
    else:
        main_logger.info(message)

def log_error(message: str, module: str = "Main"):
    """记录错误日志的便捷函数"""
    if module == "Data":
        data_logger.error(message)
    elif module == "Model":
        model_logger.error(message)
    elif module == "Training":
        training_logger.error(message)
    elif module == "Inference":
        inference_logger.error(message)
    else:
        main_logger.error(message)

def log_success(message: str, module: str = "Main"):
    """记录成功日志的便捷函数"""
    if module == "Data":
        data_logger.success(message)
    elif module == "Model":
        model_logger.success(message)
    elif module == "Training":
        training_logger.success(message)
    elif module == "Inference":
        inference_logger.success(message)
    else:
        main_logger.success(message)

if __name__ == "__main__":
    # 测试日志功能
    log_info("日志系统初始化完成")
    log_success("这是一条成功消息")
    log_error("这是一条错误消息")
    print("✅ 日志系统测试完成")
