from ultralytics import YOLO; model = YOLO('yolov8s.pt'); model.train({'batch': 4, 'data': 'data/pv_thermal_detection/data.yaml', 'degrees': 0.3, 'deterministic': True, 'device': 0, 'epochs': 100, 'exist_ok': False, 'fliplr': 0.5, 'flipud': 0.1, 'hsv_h': 0.015, 'hsv_s': 0.7, 'hsv_v': 0.4, 'imgsz': 416, 'lr0': 0.005, 'lrf': 0.01, 'mixup': 0.1, 'momentum': 0.937, 'mosaic': 1.0, 'name': 'pv_anomaly_yolov8s', 'patience': 50, 'pretrained': True, 'project': 'runs/detect', 'save': True, 'save_period': 10, 'scale': 0.5, 'seed': 0, 'translate': 0.1, 'verbose': True, 'warmup_bias_lr': 0.1, 'warmup_epochs': 5.0, 'warmup_momentum': 0.8, 'weight_decay': 0.0005, 'workers': 2, 'single_cls': False, 'rect': False, 'cos_lr': True, 'close_mosaic': 10, 'copy_paste': 0.1, 'perspective': 0.0003, 'shear': 0.2, 'auto_augment': 'randaugment', 'erasing': 0.4})