import os
import sys
import argparse
import yaml
import shutil
from pathlib import Path

def verify_dataset_paths(data_yaml_path):
    """严格验证数据集路径是否存在"""
    try:
        with open(data_yaml_path, 'r') as f:
            data_config = yaml.safe_load(f)
        
        print(f"\n验证数据集路径...")
        yaml_dir = os.path.dirname(os.path.abspath(data_yaml_path))
        print(f"数据YAML文件所在目录: {yaml_dir}")
        
        all_paths_valid = True
        
        # 检查train, val, test路径
        for key in ['train', 'val', 'test']:
            if key in data_config:
                path_str = data_config[key]
                
                # 尝试几种不同的解析方式
                possible_paths = [
                    # 相对于yaml文件的路径
                    os.path.join(yaml_dir, path_str),
                    # 相对于工作目录的路径
                    os.path.join(os.getcwd(), path_str),
                    # 原始路径（如果是绝对路径）
                    path_str
                ]
                
                found = False
                valid_path = None
                
                for test_path in possible_paths:
                    if os.path.exists(test_path):
                        found = True
                        valid_path = test_path
                        break
                
                print(f"检查 {key} 路径: {path_str}")
                if found:
                    print(f"✓ 已验证路径存在: {valid_path}")
                    # 统计图像文件数量
                    image_count = len([f for f in os.listdir(valid_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
                    print(f"  图像文件数量: {image_count}")
                else:
                    print(f"✗ 路径无效: 尝试了以下路径但都不存在:")
                    for i, p in enumerate(possible_paths):
                        print(f"  {i+1}. {p}")
                    all_paths_valid = False
        
        if not all_paths_valid:
            print("\n警告: 部分数据集路径无效。请检查配置文件。")
            
            # 尝试修复路径
            print("\n尝试修复路径...")
            yaml_dirname = os.path.basename(os.path.dirname(data_yaml_path))
            
            suggested_paths = {
                'train': f'../train/images',
                'val': f'../valid/images',
                'test': f'../test/images'
            }
            
            fixed = False
            for key in ['train', 'val', 'test']:
                if key in data_config:
                    # 检查常见路径组合是否存在
                    valid_path = None
                    suggested = suggested_paths[key]
                    test_path = os.path.join(yaml_dir, suggested)
                    
                    if os.path.exists(test_path):
                        print(f"找到有效的 {key} 路径: {suggested}")
                        data_config[key] = suggested
                        fixed = True
            
            if fixed:
                # 更新配置文件
                with open(data_yaml_path, 'w') as f:
                    yaml.dump(data_config, f, default_flow_style=False)
                print(f"已更新数据集配置文件 {data_yaml_path}")
                return True
            else:
                return False
        
        return all_paths_valid
        
    except Exception as e:
        print(f"验证数据路径时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_data_paths(data_yaml_path):
    """检查并修正数据路径"""
    try:
        with open(data_yaml_path, 'r') as f:
            data_config = yaml.safe_load(f)
        
        print(f"检查数据集配置文件: {data_yaml_path}")
        # 根目录
        base_dir = Path(os.path.dirname(data_yaml_path))
        print(f"数据集基础目录: {base_dir}")
        
        paths_fixed = False
        # 检查并更新train, val, test路径
        for key in ['train', 'val', 'test']:
            if key in data_config:
                # 获取当前路径
                curr_path = data_config[key]
                print(f"检查 {key} 路径: {curr_path}")
                
                # 检查路径是否是绝对路径
                if os.path.isabs(curr_path):
                    if not os.path.exists(curr_path):
                        print(f"绝对路径 {curr_path} 不存在")
                        paths_fixed = True
                else:
                    # 尝试各种可能的相对路径组合
                    possible_paths = [
                        os.path.join(base_dir, curr_path),  # 相对于yaml文件
                        curr_path,  # 保持原样
                        os.path.join(base_dir.parent.parent, curr_path),  # 相对于项目根目录
                    ]
                    
                    found = False
                    for path in possible_paths:
                        if os.path.exists(path):
                            print(f"找到有效路径: {path}")
                            found = True
                            if path != curr_path:
                                # 不必修改,因为相对路径已被正确解析
                                pass
                            break
                    
                    if not found:
                        print(f"警告: 所有可能的路径组合都无效")
                        # 尝试使用标准的命名模式
                        folder_name = key.replace('val', 'valid')
                        corrected_path = f"../{folder_name}/images"
                        if os.path.exists(os.path.join(base_dir, folder_name, 'images')):
                            print(f"路径 {curr_path} 不存在，已更正为 {corrected_path}")
                            data_config[key] = corrected_path
                            paths_fixed = True
        
        # 只有在路径被修改时才写回文件
        if paths_fixed:
            print("路径已修正，更新配置文件")
            with open(data_yaml_path, 'w') as f:
                yaml.dump(data_config, f, default_flow_style=False)
        else:
            print("所有路径都有效，无需修改")
        
        return True
    except Exception as e:
        print(f"检查数据路径时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_training(config_path="scripts/train_config_s.yaml"):
    """启动YOLOv8s模型训练"""
    try:
        # 读取配置文件
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # 检查数据路径
        data_yaml_path = config.get('data')
        print(f"\n检查数据集配置文件: {data_yaml_path}")
        
        # 先用旧方法检查
        if data_yaml_path and not check_data_paths(data_yaml_path):
            print("数据路径检查失败，请手动确认数据配置文件")
            return False
        
        # 再严格验证一次
        if not verify_dataset_paths(data_yaml_path):
            print("数据集路径验证失败，请手动确认数据集位置")
            return False
        
        # 打印训练参数
        print("\n========== 训练参数 ==========")
        print(f"模型: {config.get('model', 'yolov8s.pt')}")
        print(f"数据集: {config.get('data')}")
        print(f"图像尺寸: {config.get('imgsz', 416)}")
        print(f"批次大小: {config.get('batch', 4)}")
        print(f"训练轮数: {config.get('epochs', 100)}")
        print(f"学习率: {config.get('lr0', 0.005)}")
        print(f"模型名称: {config.get('name', 'pv_anomaly_yolov8s')}")
        print("===============================\n")
        
        # 直接使用YOLO API训练模型
        from ultralytics import YOLO
        
        # 初始化模型
        model = YOLO(config.get('model', 'yolov8s.pt'))
        
        # 准备训练参数
        train_args = {}
        for key, value in config.items():
            if key != 'model':  # 模型已在YOLO初始化中使用
                train_args[key] = value
        
        print("开始训练...\n")
        results = model.train(**train_args)
        
        print("\n训练完成！")
        
        # 训练完成后，复制模型到models/trained目录
        save_dir = Path(config.get('project', 'runs/detect')) / config.get('name', 'pv_anomaly_yolov8s')
        weights_path = save_dir / 'weights' / 'best.pt'
        
        # 创建trained目录（如果不存在）
        os.makedirs('models/trained', exist_ok=True)
        
        # 复制最佳模型到models/trained目录
        if os.path.exists(weights_path):
            target_path = f"models/trained/{config.get('name', 'pv_anomaly_yolov8s')}.pt"
            shutil.copy(weights_path, target_path)
            print(f"\n训练完成，最佳模型已保存至: {target_path}")
            
            # 保存评估结果
            results_path = save_dir / 'results.txt'
            if os.path.exists(results_path):
                target_results = f"models/trained/{config.get('name', 'pv_anomaly_yolov8s')}.txt"
                shutil.copy(results_path, target_results)
                print(f"评估结果已保存至: {target_results}")
            
            # 导出用于树莓派的ONNX模型
            print("\n开始导出ONNX模型...")
            os.makedirs('models/exported', exist_ok=True)
            export_path = model.export(format="onnx")
            
            if os.path.exists(export_path):
                target_onnx = f"models/exported/{config.get('name', 'pv_anomaly_yolov8s')}.onnx"
                shutil.copy(export_path, target_onnx)
                print(f"ONNX模型已导出至: {target_onnx}")
            
            return True
        else:
            print(f"\n警告: 未找到最佳模型权重文件: {weights_path}")
            return False
            
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="使用YOLOv8s训练光伏异常检测模型")
    parser.add_argument('--config', type=str, default="scripts/train_config_s.yaml", 
                        help="训练配置文件路径")
    args = parser.parse_args()
    
    # 启动训练
    start_training(args.config) 