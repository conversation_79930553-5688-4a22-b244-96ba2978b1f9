import os
import argparse
import glob
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="比较不同模型的检测结果")
    parser.add_argument("--results_dirs", nargs='+', type=str, default=[],
                        help="检测结果目录列表")
    parser.add_argument("--output_dir", type=str, default="models/comparisons",
                        help="比较结果输出目录")
    return parser.parse_args()

def parse_detection_summary(summary_file, model_dir_name):
    """解析检测摘要文件"""
    if not os.path.exists(summary_file):
        print(f"错误: 文件不存在: {summary_file}")
        return None

    model_name = model_dir_name  # 使用目录名作为默认模型名
    test_dir = ""
    conf_threshold = 0.0
    test_time = ""
    detection_counts = {}
    total_images = 0
    avg_inference_time = 0.0
    
    # 标记，表示我们是否到达了统计摘要部分
    in_summary_section = False
    
    with open(summary_file, 'r') as f:
        lines = f.readlines()
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            if line.startswith("模型:"):
                # 我们仍然读取模型名，但优先使用目录名作为标识符
                file_model_name = line.split(":", 1)[1].strip()
            elif line.startswith("测试目录:"):
                test_dir = line.split(":", 1)[1].strip()
            elif line.startswith("置信度阈值:"):
                try:
                    conf_threshold = float(line.split(":", 1)[1].strip())
                except ValueError:
                    pass
            elif line.startswith("测试时间:"):
                test_time = line.split(":", 1)[1].strip()
            elif line == "## 统计摘要":
                in_summary_section = True
            elif in_summary_section and line.startswith("- 总图像数:"):
                try:
                    total_images = int(line.split(":", 1)[1].strip())
                except ValueError:
                    pass
            elif in_summary_section and line.startswith("- 平均推理时间:"):
                try:
                    avg_inference_time = float(line.split(":", 1)[1].strip().split()[0])
                except (ValueError, IndexError):
                    pass
            elif in_summary_section and line.startswith("  - ") and ":" in line:
                parts = line.strip("  - ").split(":", 1)
                if len(parts) == 2:
                    class_name = parts[0].strip()
                    try:
                        count = int(parts[1].strip().split()[0])
                        detection_counts[class_name] = count
                    except (ValueError, IndexError):
                        pass
    
    return {
        "model_name": model_name,
        "test_dir": test_dir,
        "conf_threshold": conf_threshold,
        "test_time": test_time,
        "total_images": total_images,
        "avg_inference_time": avg_inference_time,
        "detection_counts": detection_counts
    }

def compare_detection_results(results_dirs, output_dir):
    """比较不同模型的检测结果"""
    if not results_dirs:
        print("错误: 未指定检测结果目录")
        return False
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 收集所有模型的摘要数据
    model_summaries = []
    
    for result_dir in results_dirs:
        # 使用目录名作为模型标识符
        model_dir_name = os.path.basename(result_dir)
        
        summary_file = os.path.join(result_dir, "detection_summary.txt")
        if os.path.exists(summary_file):
            summary_data = parse_detection_summary(summary_file, model_dir_name)
            if summary_data:
                model_summaries.append(summary_data)
                print(f"已加载模型 {summary_data['model_name']} 的检测摘要")
            else:
                print(f"警告: 无法解析摘要文件 {summary_file}")
        else:
            print(f"警告: 未找到摘要文件 {summary_file}")
    
    if not model_summaries:
        print("错误: 未找到有效的检测摘要")
        return False
    
    # 生成比较报告
    report_file = os.path.join(output_dir, "detection_comparison.md")
    with open(report_file, 'w') as f:
        f.write("# 模型检测结果比较\n\n")
        
        # 写入基本信息表格
        f.write("## 基本信息\n\n")
        f.write("| 模型 | 图像数量 | 平均推理时间 (ms) | 置信度阈值 |\n")
        f.write("|------|----------|-------------------|------------|\n")
        
        for summary in model_summaries:
            f.write(f"| {summary['model_name']} | {summary['total_images']} | {summary['avg_inference_time']:.1f} | {summary['conf_threshold']:.2f} |\n")
        
        f.write("\n")
        
        # 写入检测统计表格
        f.write("## 检测统计\n\n")
        
        # 获取所有类别
        all_classes = set()
        for summary in model_summaries:
            all_classes.update(summary['detection_counts'].keys())
        
        # 创建表头
        f.write("| 类别 |")
        for summary in model_summaries:
            f.write(f" {summary['model_name']} |")
        f.write("\n")
        
        # 创建分隔行
        f.write("|------|")
        for _ in model_summaries:
            f.write("------|")
        f.write("\n")
        
        # 填充数据
        for class_name in sorted(all_classes):
            f.write(f"| {class_name} |")
            for summary in model_summaries:
                count = summary['detection_counts'].get(class_name, 0)
                f.write(f" {count} |")
            f.write("\n")
        
        f.write("\n")
        
        # 写入检测比例
        f.write("## 检测比例\n\n")
        
        # 创建表头
        f.write("| 类别 |")
        for summary in model_summaries:
            f.write(f" {summary['model_name']} |")
        f.write("\n")
        
        # 创建分隔行
        f.write("|------|")
        for _ in model_summaries:
            f.write("------|")
        f.write("\n")
        
        # 填充数据
        for class_name in sorted(all_classes):
            f.write(f"| {class_name} |")
            for summary in model_summaries:
                total_detections = sum(summary['detection_counts'].values())
                count = summary['detection_counts'].get(class_name, 0)
                percentage = (count / total_detections * 100) if total_detections > 0 else 0
                f.write(f" {percentage:.1f}% |")
            f.write("\n")
    
    print(f"比较报告已保存到: {report_file}")
    
    # 生成可视化图表
    # 1. 检测数量比较柱状图
    plt.figure(figsize=(12, 8))
    
    # 准备数据
    class_names = sorted(all_classes)
    model_names = [summary['model_name'] for summary in model_summaries]
    
    # 设置图表
    x = np.arange(len(class_names))
    width = 0.8 / len(model_summaries)
    
    for i, summary in enumerate(model_summaries):
        counts = [summary['detection_counts'].get(class_name, 0) for class_name in class_names]
        offset = i * width - (len(model_summaries) - 1) * width / 2
        plt.bar(x + offset, counts, width, label=summary['model_name'])
    
    plt.xlabel('类别')
    plt.ylabel('检测数量')
    plt.title('不同模型的检测数量比较')
    plt.xticks(x, class_names, rotation=45, ha='right')
    plt.legend()
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(os.path.join(output_dir, "detection_counts_comparison.png"))
    plt.close()
    
    # 2. 检测比例饼图
    for summary in model_summaries:
        plt.figure(figsize=(10, 10))
        counts = summary['detection_counts']
        
        if counts:
            labels = counts.keys()
            sizes = counts.values()
            
            plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
            plt.axis('equal')
            plt.title(f"{summary['model_name']} 检测类别比例")
            
            # 保存图表
            plt.savefig(os.path.join(output_dir, f"detection_ratio_{summary['model_name']}.png"))
            plt.close()
        else:
            plt.close()
    
    print(f"比较图表已保存到: {output_dir}")
    return True

if __name__ == "__main__":
    args = parse_args()
    
    # 如果未指定目录，则使用默认的detection_results下的所有目录
    if not args.results_dirs:
        base_dir = "models/detection_results"
        if os.path.exists(base_dir):
            args.results_dirs = [os.path.join(base_dir, d) for d in os.listdir(base_dir) 
                               if os.path.isdir(os.path.join(base_dir, d))]
    
    # 比较检测结果
    compare_detection_results(args.results_dirs, args.output_dir) 