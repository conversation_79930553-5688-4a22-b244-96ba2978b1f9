from ultralytics import YOLO
import os
import yaml
import time
import argparse
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import sys

def parse_args():
    parser = argparse.ArgumentParser(description='训练YOLOv8模型检测光伏电板故障')
    parser.add_argument('--cfg', type=str, default='scripts/train_config.yaml', help='训练配置文件路径')
    parser.add_argument('--epochs', type=int, default=None, help='训练轮数，覆盖配置文件设置')
    parser.add_argument('--batch', type=int, default=None, help='批量大小，覆盖配置文件设置')
    parser.add_argument('--imgsz', type=int, default=None, help='图像尺寸，覆盖配置文件设置')
    parser.add_argument('--device', type=str, default=None, help='训练设备，例如 0 或 cpu')
    parser.add_argument('--resume', action='store_true', help='恢复之前的训练')
    return parser.parse_args()

def load_config(config_path):
    """加载YAML配置文件"""
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件未找到: {config_path}")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print(f"已加载配置文件: {config_path}")
        return config
    except Exception as e:
        print(f"加载配置文件时出错: {e}")
        raise

def check_dataset(data_path):
    """检查数据集是否存在并打印信息"""
    if not os.path.exists(data_path):
        raise FileNotFoundError(f"数据集配置文件未找到: {data_path}")
    
    try:
        with open(data_path, 'r', encoding='utf-8') as f:
            data_config = yaml.safe_load(f)
        
        print("\n数据集信息:")
        print(f"- 训练集路径: {data_config.get('train', 'unknown')}")
        print(f"- 验证集路径: {data_config.get('val', 'unknown')}")
        print(f"- 测试集路径: {data_config.get('test', 'unknown')}")
        print(f"- 类别数量: {data_config.get('nc', 'unknown')}")
        print(f"- 类别名称: {data_config.get('names', [])}")
        
        # 检查训练集图像
        data_dir = os.path.dirname(data_path)
        train_path = data_config.get('train', '')
        if train_path:
            train_dir = os.path.join(data_dir, train_path)
            
            if os.path.exists(train_dir):
                if os.path.isdir(train_dir):
                    image_count = len([f for f in os.listdir(train_dir) if f.endswith(('.jpg', '.jpeg', '.png'))])
                    print(f"- 训练图像数量: {image_count}")
                else:
                    print(f"警告: 训练图像路径不是目录: {train_dir}")
            else:
                print(f"警告: 训练图像目录不存在: {train_dir}")
                
                # 尝试其他可能的路径
                alt_path = os.path.join(data_dir, 'train', 'images')
                if os.path.exists(alt_path):
                    image_count = len([f for f in os.listdir(alt_path) if f.endswith(('.jpg', '.jpeg', '.png'))])
                    print(f"- 找到替代训练图像目录: {alt_path}")
                    print(f"- 训练图像数量: {image_count}")
                    
                    # 更新配置
                    data_config['train'] = os.path.relpath(alt_path, data_dir)
                    with open(data_path, 'w', encoding='utf-8') as f:
                        yaml.dump(data_config, f, default_flow_style=False)
                    print(f"- 已更新训练路径为: {data_config['train']}")
        
        return data_config
    except Exception as e:
        print(f"检查数据集时出错: {e}")
        raise

def visualize_results(results_path):
    """可视化训练结果"""
    results_dir = Path(results_path)
    
    # 查找结果CSV文件
    csv_files = list(results_dir.glob('*.csv'))
    if not csv_files:
        print("未找到训练结果CSV文件")
        return
    
    try:
        # 读取结果文件
        results_file = csv_files[0]
        results_data = np.loadtxt(results_file, delimiter=',', skiprows=1)
        
        # 创建图表
        plt.figure(figsize=(15, 10))
        
        # 绘制损失曲线
        plt.subplot(2, 2, 1)
        plt.plot(results_data[:, 0], results_data[:, 1], label='训练损失')
        plt.plot(results_data[:, 0], results_data[:, 2], label='验证损失')
        plt.xlabel('轮数')
        plt.ylabel('损失')
        plt.title('训练和验证损失')
        plt.legend()
        plt.grid(True)
        
        # 绘制mAP@0.5曲线
        plt.subplot(2, 2, 2)
        plt.plot(results_data[:, 0], results_data[:, 3], label='mAP@0.5')
        plt.xlabel('轮数')
        plt.ylabel('mAP@0.5')
        plt.title('模型mAP@0.5性能')
        plt.legend()
        plt.grid(True)
        
        # 绘制mAP@0.5:0.95曲线
        plt.subplot(2, 2, 3)
        plt.plot(results_data[:, 0], results_data[:, 4], label='mAP@0.5:0.95')
        plt.xlabel('轮数')
        plt.ylabel('mAP@0.5:0.95')
        plt.title('模型mAP@0.5:0.95性能')
        plt.legend()
        plt.grid(True)
        
        # 绘制精确率和召回率
        plt.subplot(2, 2, 4)
        plt.plot(results_data[:, 0], results_data[:, 5], label='精确率')
        plt.plot(results_data[:, 0], results_data[:, 6], label='召回率')
        plt.xlabel('轮数')
        plt.ylabel('值')
        plt.title('精确率和召回率')
        plt.legend()
        plt.grid(True)
        
        # 保存图表
        plt.tight_layout()
        output_path = results_dir / 'training_visualization.png'
        plt.savefig(output_path)
        plt.close()
        
        print(f"训练过程可视化结果已保存到 {output_path}")
    except Exception as e:
        print(f"可视化训练结果时出错: {e}")

def main():
    try:
        # 确保UTF-8输出
        if sys.platform.startswith('win'):
            sys.stdout.reconfigure(encoding='utf-8', errors='replace')
            
        # 解析命令行参数
        args = parse_args()
        
        # 加载配置文件
        try:
            config = load_config(args.cfg)
        except FileNotFoundError:
            print(f"找不到配置文件: {args.cfg}")
            print("尝试使用默认配置...")
            
            # 尝试默认路径
            default_cfg_paths = [
                'train_config.yaml',
                'scripts/train_config.yaml',
                'config/train_config.yaml'
            ]
            
            for cfg_path in default_cfg_paths:
                if os.path.exists(cfg_path):
                    print(f"找到配置文件: {cfg_path}")
                    config = load_config(cfg_path)
                    break
            else:
                # 如果找不到任何配置文件，使用默认配置
                print("未找到任何配置文件，使用默认配置")
                config = {
                    'data': 'data/pv_thermal_detection/data.yaml',
                    'model': 'yolov8n.pt',
                    'epochs': 50,
                    'batch': 8,
                    'imgsz': 640,
                    'name': 'pv_thermal_detection'
                }
        
        # 检查数据集
        data_path = config.get('data')
        if not data_path:
            # 尝试查找数据集
            possible_data_paths = [
                'data/pv_thermal_detection/data.yaml',
                'data/data.yaml'
            ]
            
            for path in possible_data_paths:
                if os.path.exists(path):
                    data_path = path
                    config['data'] = path
                    print(f"找到数据集配置: {path}")
                    break
            else:
                raise FileNotFoundError("找不到数据集配置文件")
        
        data_config = check_dataset(data_path)
        
        # 创建模型输出目录
        os.makedirs("models", exist_ok=True)
        
        # 训练开始时间
        start_time = time.time()
        
        # 加载预训练模型
        model_path = config.get('model', 'yolov8n.pt')
        print(f"\n加载预训练模型: {model_path}")
        
        # 如果模型文件不存在且不是标准模型名，尝试下载
        if not os.path.exists(model_path) and not model_path.startswith('yolov8'):
            print(f"警告: 模型文件不存在: {model_path}")
            model_path = 'yolov8n.pt'  # 退回到最小的模型
            print(f"使用默认模型: {model_path}")
        
        model = YOLO(model_path)
        
        # 更新配置参数
        train_args = {k: v for k, v in config.items() if k != 'model'}
        
        # 命令行参数覆盖配置文件
        if args.epochs is not None:
            train_args['epochs'] = args.epochs
        if args.batch is not None:
            train_args['batch'] = args.batch
        if args.imgsz is not None:
            train_args['imgsz'] = args.imgsz
        if args.device is not None:
            train_args['device'] = args.device
        if args.resume:
            train_args['resume'] = True
        
        # 打印训练参数
        print("\n训练参数:")
        for k, v in train_args.items():
            print(f"- {k}: {v}")
        
        # 训练模型
        print("\n开始训练...")
        results = model.train(**train_args)
        
        # 计算训练时间
        training_time = time.time() - start_time
        hours, remainder = divmod(training_time, 3600)
        minutes, seconds = divmod(remainder, 60)
        print(f"\n训练完成! 总用时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
        
        # 可视化训练结果
        results_dir = os.path.join(config.get('project', 'runs/detect'), 
                                  config.get('name', 'pv_thermal_detection'))
        visualize_results(results_dir)
        
        # 导出模型
        print("\n导出模型...")
        try:
            # 保存最佳模型的ONNX版本
            model.export(format="onnx", dynamic=True, simplify=True)
            print(f"模型已导出为ONNX格式，适合部署到树莓派")
        except Exception as e:
            print(f"导出模型时出错: {e}")
            print("跳过模型导出，继续评估...")
        
        # 在验证集上评估模型性能
        print("\n评估模型性能...")
        try:
            val_results = model.val(split='val')
            
            print("\n模型性能评估:")
            print(f"mAP@0.5: {val_results.box.map50:.4f}")  # mAP at IoU=0.5
            print(f"mAP@0.5:0.95: {val_results.box.map:.4f}")  # mAP at IoU=0.5:0.95
            print(f"精确率 (Precision): {val_results.box.mp:.4f}")
            print(f"召回率 (Recall): {val_results.box.mr:.4f}")
            
            # 按类别打印性能
            print("\n各类别性能:")
            for i, name in enumerate(data_config.get('names', [])):
                if i < len(val_results.box.maps50):
                    print(f"- {name}:")
                    print(f"  mAP@0.5: {val_results.box.maps50[i]:.4f}")
                    print(f"  精确率: {val_results.box.mps[i]:.4f}")
                    print(f"  召回率: {val_results.box.mrs[i]:.4f}")
            
            # 打印模型大小信息
            model_paths = {
                "best": os.path.join(results_dir, 'weights', 'best.pt'),
                "last": os.path.join(results_dir, 'weights', 'last.pt'),
                "onnx": os.path.join(results_dir, 'weights', 'best.onnx')
            }
            
            print("\n模型文件信息:")
            for name, path in model_paths.items():
                if os.path.exists(path):
                    size_mb = os.path.getsize(path) / (1024 * 1024)
                    print(f"- {name}: {path} ({size_mb:.2f} MB)")
        except Exception as e:
            print(f"评估模型性能时出错: {e}")
        
        print("\n模型训练与评估完成！")
    
    except Exception as e:
        print(f"训练过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main() 