import sys
import os
import gc
import yaml
from ultralytics import YOLO

# 强制使用CPU
os.environ["CUDA_VISIBLE_DEVICES"] = ""

# 显示当前配置
config_path = "scripts/train_config.yaml"
with open(config_path, 'r') as f:
    config = yaml.safe_load(f)
    print("训练配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")

# 定期执行垃圾回收
gc.enable()
gc.collect()

# 导入模型
try:
    model = YOLO(config['model'])
    print(f"成功加载模型: {config['model']}")
except Exception as e:
    print(f"加载模型出错: {e}")
    sys.exit(1)

# 开始训练
try:
    print("\n开始训练...")
    # 从配置文件中移除model键，因为已经在YOLO()中使用了
    train_args = {k: v for k, v in config.items() if k != 'model'}
    
    # 进行简短训练
    model.train(**train_args)
    
    print("\n训练成功完成!")
except Exception as e:
    print(f"\n训练过程中出错: {e}")
    import traceback
    traceback.print_exc() 