import argparse
from ultralytics import YOLO
import cv2
import numpy as np
import os
import glob
import time
from pathlib import Path

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="测试YOLOv8模型在图像上的表现")
    parser.add_argument("--model_path", type=str, default="runs/detect/pv_anomaly_yolov8s4/weights/best.pt", 
                        help="模型文件路径")
    parser.add_argument("--test_dir", type=str, default="data/pv_thermal_detection/test/images",
                        help="测试图像目录")
    parser.add_argument("--conf", type=float, default=0.25,
                        help="置信度阈值")
    parser.add_argument("--batch", type=int, default=1, 
                        help="批处理大小")
    parser.add_argument("--output_dir", type=str, default="",
                        help="结果输出目录，默认为models/detection_results/<model_name>")
    return parser.parse_args()

def test_on_images(model_path, test_dir, output_dir, conf_threshold=0.25):
    """
    在测试图像上运行模型并显示结果
    
    参数:
        model_path: 模型文件路径
        test_dir: 测试图像目录
        output_dir: 输出目录
        conf_threshold: 置信度阈值
    """
    # 加载模型
    print(f"加载模型 {model_path}...")
    model = YOLO(model_path)
    
    # 获取测试图像
    image_extensions = ['*.jpg', '*.jpeg', '*.png']
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(test_dir, ext)))
    
    if not image_files:
        print(f"错误: 在 {test_dir} 中找不到图像文件")
        return
    
    print(f"找到 {len(image_files)} 个测试图像")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建摘要文件
    summary_file = os.path.join(output_dir, "detection_summary.txt")
    with open(summary_file, "w") as f:
        f.write(f"# 检测结果摘要\n\n")
        f.write(f"模型: {os.path.basename(model_path)}\n")
        f.write(f"测试目录: {test_dir}\n")
        f.write(f"置信度阈值: {conf_threshold}\n")
        f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"## 检测结果\n\n")
    
    # 计数器
    detection_counts = {}
    total_inference_time = 0
    
    # 遍历所有测试图像
    for img_path in image_files:
        img_name = os.path.basename(img_path)
        print(f"处理图像: {img_name}")
        
        # 读取图像
        img = cv2.imread(img_path)
        if img is None:
            print(f"无法读取图像 {img_path}")
            continue
            
        # 记录推理时间
        start_time = time.time()
        
        # 运行推理
        results = model(img, conf=conf_threshold)[0]
        
        # 计算推理时间
        inference_time = time.time() - start_time
        total_inference_time += inference_time
        
        # 在图像上绘制结果
        result_img = results.plot()
        
        # 添加推理时间
        cv2.putText(
            result_img, 
            f"Inference: {inference_time*1000:.1f} ms", 
            (10, 30), 
            cv2.FONT_HERSHEY_SIMPLEX, 
            1, 
            (0, 0, 255), 
            2
        )
        
        # 保存结果图像
        output_path = os.path.join(output_dir, f"result_{img_name}")
        cv2.imwrite(output_path, result_img)
        
        # 打印检测结果
        print(f"  检测到 {len(results.boxes)} 个对象")
        
        # 更新摘要文件
        with open(summary_file, "a") as f:
            f.write(f"### 图像: {img_name}\n")
            f.write(f"- 检测到 {len(results.boxes)} 个对象\n")
            f.write(f"- 推理时间: {inference_time*1000:.1f} 毫秒\n")
            
            # 记录检测到的对象
            if len(results.boxes) > 0:
                f.write("- 检测结果:\n")
                
                for i, box in enumerate(results.boxes):
                    cls_id = int(box.cls[0].item())
                    cls_name = results.names[cls_id]
                    conf = box.conf[0].item()
                    x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())
                    
                    # 更新检测计数器
                    if cls_name not in detection_counts:
                        detection_counts[cls_name] = 0
                    detection_counts[cls_name] += 1
                    
                    # 写入摘要
                    f.write(f"  - {i+1}: {cls_name} (置信度: {conf:.2f}) 位置: [{x1}, {y1}, {x2}, {y2}]\n")
            
            f.write("\n")
        
        # 打印每个检测对象的详情
        for i, box in enumerate(results.boxes):
            cls_id = int(box.cls[0].item())
            cls_name = results.names[cls_id]
            conf = box.conf[0].item()
            x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())
            print(f"  - {i+1}: {cls_name} (置信度: {conf:.2f}) 位置: [{x1}, {y1}, {x2}, {y2}]")
            
        print(f"  结果已保存到 {output_path}")
        print(f"  推理时间: {inference_time*1000:.1f} 毫秒")
        print()
    
    # 计算平均推理时间
    avg_inference_time = total_inference_time / len(image_files) if image_files else 0
    
    # 添加统计摘要
    with open(summary_file, "a") as f:
        f.write(f"## 统计摘要\n\n")
        f.write(f"- 总图像数: {len(image_files)}\n")
        f.write(f"- 平均推理时间: {avg_inference_time*1000:.1f} 毫秒\n")
        f.write(f"- 各类别检测统计:\n")
        
        for cls_name, count in sorted(detection_counts.items(), key=lambda x: x[1], reverse=True):
            f.write(f"  - {cls_name}: {count} 个\n")
    
    print(f"所有结果已保存到 {output_dir} 目录")
    print(f"检测摘要已保存到 {summary_file}")
    print(f"总计处理了 {len(image_files)} 张图像，平均推理时间: {avg_inference_time*1000:.1f} 毫秒")
    print(f"检测统计:")
    for cls_name, count in sorted(detection_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  - {cls_name}: {count} 个")


if __name__ == "__main__":
    # 解析命令行参数
    args = parse_args()
    
    # 模型路径
    model_path = args.model_path
    
    # 检查模型是否存在
    if not os.path.exists(model_path):
        # 检查是否有其他权重文件
        weight_files = glob.glob("runs/detect/pv_anomaly_yolov8s4/weights/*.pt")
        if weight_files:
            model_path = weight_files[0]
            print(f"找不到指定模型，使用替代模型: {model_path}")
        else:
            print("错误：找不到训练好的模型文件。请先运行 train_model.py 训练模型。")
            exit(1)
    
    # 测试图像目录
    test_dir = args.test_dir
    
    # 如果测试目录不存在，尝试使用验证集
    if not os.path.exists(test_dir):
        test_dir = "data/pv_thermal_detection/valid/images"
        if not os.path.exists(test_dir):
            print("错误：找不到测试图像目录。请确保数据集已正确下载。")
            exit(1)
    
    # 确定输出目录
    if args.output_dir:
        output_dir = args.output_dir
    else:
        # 从模型路径中提取模型名称
        model_name = os.path.splitext(os.path.basename(model_path))[0]
        # 从路径中提取模型类型/版本
        model_type = Path(model_path).parts[-3]  # 例如: pv_anomaly_yolov8s4
        output_dir = f"models/detection_results/{model_type}"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    print(f"检测结果将保存到: {output_dir}")
    
    # 运行测试
    test_on_images(model_path, test_dir, output_dir, args.conf) 