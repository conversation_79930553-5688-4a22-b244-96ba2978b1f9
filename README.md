# 光伏电板故障检测系统

基于YOLOv8n的光伏电板热敏故障检测系统，可训练模型并部署到树莓派上。

## 项目结构

```
PV_terminal_detection/
├── data/                      # 数据集目录
│   └── pv_thermal_detection/  # 从Roboflow下载的数据集
├── models/                    # 模型保存目录
├── scripts/                   # 脚本目录
│   ├── download_dataset.py    # 下载数据集脚本
│   ├── train_model.py         # 模型训练脚本
│   ├── test_model.py          # 模型测试脚本
│   └── deploy_to_raspi.py     # 部署到树莓派脚本
├── runs/                      # 训练运行结果
└── raspberry_pi_model/        # 树莓派部署模型和脚本
```

## 使用步骤

### 1. 准备环境

确保已安装Anaconda，并创建一个conda环境：

```bash
# 在Anaconda Prompt中执行
conda create -n pv_detect python=3.9 -y
conda activate pv_detect
```

### 2. 下载数据集

运行以下命令下载数据集：

```bash
# 进入项目目录
cd PV_terminal_detection

# 激活环境
conda activate pv_detect

# 安装必要的库
pip install ultralytics matplotlib opencv-python roboflow

# 下载数据集
# 注意：需要在scripts/download_dataset.py中添加您的Roboflow API密钥
python scripts/download_dataset.py
```

### 3. 训练模型

```bash
python scripts/train_model.py
```

这将训练YOLOv8n模型并保存到runs/detect/pv_thermal_detection目录下。

### 4. 测试模型

```bash
python scripts/test_model.py
```

测试结果将保存在results目录下。

### 5. 部署到树莓派

```bash
python scripts/deploy_to_raspi.py
```

这将生成适合树莓派运行的模型和脚本，保存在raspberry_pi_model目录下。

## 树莓派部署

1. 将raspberry_pi_model目录下的内容复制到树莓派上
2. 在树莓派上安装必要的依赖（详见raspberry_pi_model/README.md）
3. 运行detect.py进行实时检测

## 模型性能评估

模型训练完成后，您可以在训练日志中查看以下指标：

- mAP@0.5: 在IoU=0.5阈值下的平均精度
- mAP@0.5:0.95: 在多个IoU阈值下的平均精度
- 精确率 (Precision): 正确检测与总检测的比率
- 召回率 (Recall): 正确检测与总目标的比率

## 参考资源

- [Ultralytics YOLOv8文档](https://docs.ultralytics.com/)
- [Roboflow数据集](https://universe.roboflow.com/project/pv-thermal-detection/dataset/1) 