#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
光伏电板热成像故障检测 - 统一训练平台重定向脚本
该脚本已迁移到scripts/pv_thermal_trainer.py
"""

import os
import sys

def main():
    """引导用户使用新的训练平台"""
    print("\n" + "="*80)
    print("提示: 训练平台已迁移")
    print("="*80)
    print("\n统一训练平台已迁移到新的位置！")
    print("\n请使用以下方式启动训练平台:")
    print("1. 直接运行: python train.py")
    print("2. 或运行: python scripts/pv_thermal_trainer.py")
    
    response = input("\n是否立即启动新的训练平台? (y/n): ")
    if response.lower() == 'y':
        print("\n启动新的训练平台...")
        
        # 获取脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 尝试启动新的训练脚本
        if os.path.exists(os.path.join(script_dir, "train.py")):
            os.system(f"{sys.executable} {os.path.join(script_dir, 'train.py')}")
        elif os.path.exists(os.path.join(script_dir, "scripts", "pv_thermal_trainer.py")):
            os.system(f"{sys.executable} {os.path.join(script_dir, 'scripts', 'pv_thermal_trainer.py')}")
        else:
            print("\n错误: 找不到新的训练平台脚本")
            print("请确保以下文件之一存在:")
            print(f"- {os.path.join(script_dir, 'train.py')}")
            print(f"- {os.path.join(script_dir, 'scripts', 'pv_thermal_trainer.py')}")
    else:
        print("\n您可以稍后手动启动新的训练平台")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 