import os
import shutil
import glob
from pathlib import Path
import yaml
import time

def create_directories():
    """创建标准化的项目目录结构"""
    directories = [
        "models/trained",        # 训练好的模型
        "models/exported",       # 导出的模型（ONNX等）
        "models/pretrained",     # 预训练模型
        "results/predictions",   # 预测结果
        "results/reports",       # 性能报告
        "configs"                # 配置文件
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"创建目录: {directory}")

def rename_config_files():
    """规范化配置文件命名"""
    config_files = {
        "scripts/train_config.yaml": "configs/yolov8n_baseline.yaml",
        "scripts/train_config_s.yaml": "configs/yolov8s_standard.yaml",
        "scripts/train_config_improved.yaml": "configs/yolov8n_enhanced.yaml",
        "scripts/train_config_hybrid.yaml": "configs/yolov8_hybrid.yaml"
    }
    
    for src, dst in config_files.items():
        if os.path.exists(src):
            if not os.path.exists(os.path.dirname(dst)):
                os.makedirs(os.path.dirname(dst), exist_ok=True)
            
            # 如果目标文件已存在，先备份
            if os.path.exists(dst):
                backup_path = f"{dst}.bak.{int(time.time())}"
                shutil.copy2(dst, backup_path)
                print(f"备份配置文件: {dst} → {backup_path}")
            
            # 复制文件
            shutil.copy2(src, dst)
            print(f"复制配置文件: {src} → {dst}")
            
            # 更新配置文件中的名称
            update_config_name(dst)

def update_config_name(config_path):
    """更新配置文件中的name字段为规范化名称"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 获取基本名称
        base_name = os.path.basename(config_path).replace('.yaml', '')
        
        # 更新名称
        if 'name' in config:
            old_name = config['name']
            new_name = f"pv_anomaly_{base_name}_v1"
            config['name'] = new_name
            print(f"更新训练名称: {old_name} → {new_name}")
            
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, sort_keys=False)
    except Exception as e:
        print(f"更新配置名称时出错: {e}")

def copy_best_models():
    """复制所有最佳模型到统一位置"""
    # 查找所有训练结果目录
    result_dirs = glob.glob("runs/detect/*/")
    
    for result_dir in result_dirs:
        # 检查是否存在最佳模型
        best_model_path = os.path.join(result_dir, "weights/best.pt")
        if os.path.exists(best_model_path):
            # 提取训练名称
            dir_name = os.path.basename(os.path.dirname(result_dir))
            
            # 规范化名称
            if dir_name.startswith("pv_thermal_detection"):
                if "_s" in dir_name:
                    model_type = "yolov8s"
                elif "_improved" in dir_name:
                    model_type = "yolov8n_enhanced"
                elif "_hybrid" in dir_name:
                    model_type = "yolov8_hybrid"
                elif "_test" in dir_name:
                    model_type = f"test_{dir_name.split('_')[-1]}"
                else:
                    model_type = "yolov8n"
                
                # 创建目标路径
                version = dir_name.split("_")[-1] if dir_name[-1].isdigit() else "v1"
                target_name = f"pv_anomaly_{model_type}_{version}.pt"
                target_path = os.path.join("models/trained", target_name)
                
                # 复制文件
                shutil.copy2(best_model_path, target_path)
                print(f"复制模型: {best_model_path} → {target_path}")

def create_model_info():
    """为每个模型创建信息文件"""
    model_files = glob.glob("models/trained/*.pt")
    
    for model_file in model_files:
        info_file = model_file.replace('.pt', '.txt')
        
        if not os.path.exists(info_file):
            model_name = os.path.basename(model_file)
            
            with open(info_file, 'w', encoding='utf-8') as f:
                f.write(f"模型: {model_name}\n")
                f.write(f"创建时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"路径: {model_file}\n")
                f.write("描述: 光伏电板热成像异常检测模型\n")
                f.write("类别:\n")
                f.write("- Diode anomaly (二极管异常)\n")
                f.write("- Hot Spots (热点)\n")
                f.write("- Reverse polarity (极性反转)\n")
                f.write("- Vegetation (植被)\n")
                
            print(f"创建模型信息文件: {info_file}")

def main():
    print("="*80)
    print("开始清理和规范化项目")
    print("="*80)
    
    # 创建标准化目录结构
    create_directories()
    
    # 规范化配置文件
    rename_config_files()
    
    # 复制最佳模型
    copy_best_models()
    
    # 创建模型信息文件
    create_model_info()
    
    print("\n项目清理和规范化完成!")

if __name__ == "__main__":
    main() 