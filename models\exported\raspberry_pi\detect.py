
import cv2
import numpy as np
import argparse
import os

def detect_objects(image_path, model_path, conf_threshold=0.25):
    # 加载模型
    net = cv2.dnn.readNetFromONNX(model_path)
    
    # 类别名称
    classes = ["Diode anomaly", "Hot Spots", "Reverse polarity", "Vegetation"]
    
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图像: {image_path}")
        return
    
    # 预处理
    blob = cv2.dnn.blobFromImage(img, 1/255.0, (640, 640), swapRB=True, crop=False)
    net.setInput(blob)
    
    # 推理
    outputs = net.forward()
    
    # 后处理
    for detection in outputs[0]:
        confidence = detection[4]
        if confidence > conf_threshold:
            class_id = np.argmax(detection[5:])
            class_score = detection[5 + class_id]
            if class_score > conf_threshold:
                # 计算边界框
                x, y, w, h = detection[0:4]
                left = int((x - w/2) * img.shape[1])
                top = int((y - h/2) * img.shape[0])
                width = int(w * img.shape[1])
                height = int(h * img.shape[0])
                
                # 绘制边界框
                color = (0, 255, 0)
                cv2.rectangle(img, (left, top), (left + width, top + height), color, 2)
                
                # 显示标签
                label = f"{classes[class_id]}: {class_score:.2f}"
                cv2.putText(img, label, (left, top - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    
    # 显示结果
    cv2.imshow("Detection Result", img)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="光伏电板热成像故障检测")
    parser.add_argument("--image", help="图片路径", required=True)
    parser.add_argument("--model", help="ONNX模型路径", default="")
    parser.add_argument("--conf", type=float, default=0.25, help="置信度阈值")
    args = parser.parse_args()
    
    # 如果未指定模型，使用目录中最新的模型
    model_path = args.model
    if not model_path:
        models = [f for f in os.listdir(os.path.dirname(__file__)) if f.endswith('.onnx')]
        if models:
            model_path = os.path.join(os.path.dirname(__file__), sorted(models)[-1])
            print(f"使用模型: {model_path}")
        else:
            print("错误: 未找到ONNX模型文件")
            exit(1)
    
    detect_objects(args.image, model_path, args.conf)
