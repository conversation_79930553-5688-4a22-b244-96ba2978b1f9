"""
分层光伏面板故障检测系统
使用YOLOv8n快速初筛，YOLOv8s精确检测
"""

from ultralytics import YOLO
import cv2
import os
import time
import argparse
import numpy as np
from PIL import Image

def parse_args():
    parser = argparse.ArgumentParser(description='分层光伏面板故障检测系统')
    parser.add_argument('--light-model', type=str, default='runs/detect/pv_thermal_detection2/weights/best.pt',
                        help='轻量级模型路径(YOLOv8n)')
    parser.add_argument('--heavy-model', type=str, default='runs/detect/pv_thermal_detection_s/weights/best.pt',
                        help='高精度模型路径(YOLOv8s)')
    parser.add_argument('--img-dir', type=str, default='data/pv_thermal_detection/test/images',
                        help='测试图像目录')
    parser.add_argument('--output-dir', type=str, default='hierarchical_results',
                        help='结果输出目录')
    parser.add_argument('--conf-light', type=float, default=0.25,
                        help='轻量级模型置信度阈值')
    parser.add_argument('--conf-heavy', type=float, default=0.3,
                        help='高精度模型置信度阈值')
    parser.add_argument('--device', type=str, default='cuda:0',
                        help='设备 (cuda:0 或 cpu)')
    return parser.parse_args()

class HierarchicalDetector:
    """分层检测器"""
    
    def __init__(self, light_model_path, heavy_model_path, conf_light=0.25, conf_heavy=0.3, device='cuda:0'):
        # 加载轻量级模型
        print(f"加载轻量级模型: {light_model_path}")
        self.light_model = YOLO(light_model_path)
        
        # 检查高精度模型是否存在
        if os.path.exists(heavy_model_path):
            print(f"加载高精度模型: {heavy_model_path}")
            self.heavy_model = YOLO(heavy_model_path)
        else:
            print(f"警告: 高精度模型 {heavy_model_path} 不存在，将只使用轻量级模型")
            self.heavy_model = None
        
        # 设置阈值
        self.conf_light = conf_light
        self.conf_heavy = conf_heavy
        
        # 设置设备
        self.device = device
        
        # 类别特定阈值
        self.class_thresholds = {
            'Diode anomaly': 0.3,  # 二极管异常
            'Hot Spots': 0.4,      # 热点
            'Reverse polarity': 0.6, # 反向极性 
            'Vegetation': 0.25     # 植被
        }
        
    def detect(self, image_path):
        """对单个图像进行分层检测"""
        # 记录开始时间
        start_time = time.time()
        
        # 阶段1: 使用轻量级模型初筛
        light_start = time.time()
        light_results = self.light_model.predict(
            image_path, 
            conf=self.conf_light, 
            device=self.device
        )[0]
        light_time = time.time() - light_start
        
        # 如果没有高精度模型或轻量级模型没有检测到任何结果，直接返回轻量级结果
        if self.heavy_model is None or len(light_results.boxes) == 0:
            total_time = time.time() - start_time
            return light_results, None, light_time, 0, total_time
        
        # 获取轻量级模型检测到的边界框
        boxes = light_results.boxes.xyxy.cpu().numpy()
        
        # 阶段2: 对检测到的区域使用高精度模型
        heavy_results = []
        heavy_time_start = time.time()
        
        # 读取原始图像
        original_img = cv2.imread(image_path)
        img_height, img_width = original_img.shape[:2]
        
        # 处理每个检测到的边界框
        for i, box in enumerate(boxes):
            # 扩大边界框 (增加10%外边距)
            x1, y1, x2, y2 = box
            w, h = x2 - x1, y2 - y1
            x1 = max(0, x1 - w * 0.1)
            y1 = max(0, y1 - h * 0.1)
            x2 = min(img_width, x2 + w * 0.1)
            y2 = min(img_height, y2 + h * 0.1)
            
            # 裁剪边界框区域
            crop_img = original_img[int(y1):int(y2), int(x1):int(x2)]
            
            # 如果裁剪区域太小，跳过
            if crop_img.size == 0 or crop_img.shape[0] < 10 or crop_img.shape[1] < 10:
                continue
                
            # 保存临时裁剪图像
            tmp_crop_path = f"tmp_crop_{i}.jpg"
            cv2.imwrite(tmp_crop_path, crop_img)
            
            # 使用高精度模型
            crop_result = self.heavy_model.predict(
                tmp_crop_path, 
                conf=self.conf_heavy,
                device=self.device
            )[0]
            
            # 删除临时文件
            os.remove(tmp_crop_path)
            
            # 调整检测结果的坐标（从裁剪图像坐标转回原图坐标）
            for crop_box in crop_result.boxes:
                # 获取相对裁剪图的坐标
                cx1, cy1, cx2, cy2 = crop_box.xyxy[0].cpu().numpy()
                
                # 转换回原图坐标
                ox1 = x1 + cx1
                oy1 = y1 + cy1
                ox2 = x1 + cx2
                oy2 = y1 + cy2
                
                # 记录结果
                heavy_results.append({
                    'box': [ox1, oy1, ox2, oy2],
                    'cls': int(crop_box.cls[0].item()),
                    'conf': float(crop_box.conf[0].item()),
                    'name': crop_result.names[int(crop_box.cls[0].item())]
                })
        
        heavy_time = time.time() - heavy_time_start
        total_time = time.time() - start_time
        
        return light_results, heavy_results, light_time, heavy_time, total_time
    
    def visualize_results(self, image_path, light_results, heavy_results, output_path, show_times=None):
        """可视化检测结果"""
        # 读取原始图像
        img = cv2.imread(image_path)
        
        # 绘制轻量级模型结果（红色）
        if light_results is not None:
            for i, box in enumerate(light_results.boxes):
                x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())
                cls_id = int(box.cls[0].item())
                conf = box.conf[0].item()
                cls_name = light_results.names[cls_id]
                
                # 应用类别特定阈值
                threshold = self.class_thresholds.get(cls_name, self.conf_light)
                if conf < threshold:
                    continue
                
                # 绘制边界框
                cv2.rectangle(img, (x1, y1), (x2, y2), (0, 0, 255), 1)
                
                # 准备标签
                label = f"{cls_name} {conf:.2f}"
                cv2.putText(img, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
        
        # 绘制高精度模型结果（绿色）
        if heavy_results:
            for result in heavy_results:
                x1, y1, x2, y2 = map(int, result['box'])
                cls_name = result['name']
                conf = result['conf']
                
                # 应用类别特定阈值
                threshold = self.class_thresholds.get(cls_name, self.conf_heavy)
                if conf < threshold:
                    continue
                
                # 绘制边界框
                cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # 准备标签
                label = f"{cls_name} {conf:.2f}"
                cv2.putText(img, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        # 添加性能信息
        if show_times:
            light_time, heavy_time, total_time = show_times
            perf_text = f"轻量级: {light_time*1000:.1f}ms, 高精度: {heavy_time*1000:.1f}ms, 总计: {total_time*1000:.1f}ms"
            cv2.putText(img, perf_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
        
        # 保存结果
        cv2.imwrite(output_path, img)
        print(f"结果已保存: {output_path}")

def main():
    # 解析参数
    args = parse_args()
    
    # 检查高精度模型是否存在
    if not os.path.exists(args.light_model):
        print(f"错误: 轻量级模型 {args.light_model} 不存在")
        return
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化分层检测器
    detector = HierarchicalDetector(
        args.light_model,
        args.heavy_model,
        args.conf_light,
        args.conf_heavy,
        args.device
    )
    
    # 处理图像
    image_files = [f for f in os.listdir(args.img_dir) 
                  if f.endswith(('.jpg', '.jpeg', '.png'))]
    
    print(f"找到 {len(image_files)} 个图像，准备处理...")
    
    total_light_time = 0
    total_heavy_time = 0
    total_time = 0
    
    # 处理每个图像
    for i, image_file in enumerate(image_files[:10]):  # 限制处理前10张图像
        image_path = os.path.join(args.img_dir, image_file)
        print(f"\n处理图像 {i+1}/{min(len(image_files), 10)}: {image_file}")
        
        # 执行分层检测
        light_results, heavy_results, light_time, heavy_time, total_detection_time = detector.detect(image_path)
        
        # 累计时间
        total_light_time += light_time
        total_heavy_time += heavy_time
        total_time += total_detection_time
        
        # 输出路径
        output_path = os.path.join(args.output_dir, f"hier_{image_file}")
        
        # 轻量级检测结果数量
        light_count = len(light_results.boxes) if light_results else 0
        
        # 高精度检测结果数量
        heavy_count = len(heavy_results) if heavy_results else 0
        
        print(f"轻量级模型检测到 {light_count} 个对象，用时 {light_time*1000:.1f}ms")
        print(f"高精度模型检测到 {heavy_count} 个对象，用时 {heavy_time*1000:.1f}ms")
        print(f"总检测时间: {total_detection_time*1000:.1f}ms")
        
        # 可视化结果
        detector.visualize_results(
            image_path, 
            light_results, 
            heavy_results, 
            output_path,
            (light_time, heavy_time, total_detection_time)
        )
    
    # 输出平均性能
    if len(image_files) > 0:
        processed_count = min(len(image_files), 10)
        print(f"\n处理了 {processed_count} 张图像的性能统计:")
        print(f"轻量级模型平均时间: {total_light_time/processed_count*1000:.1f}ms")
        print(f"高精度模型平均时间: {total_heavy_time/processed_count*1000:.1f}ms")
        print(f"总平均处理时间: {total_time/processed_count*1000:.1f}ms")
        
        if heavy_results is None:
            print("\n注意: 高精度模型未找到，只使用了轻量级模型。请先训练YOLOv8s模型。")
            print("可以使用以下命令训练高精度模型:")
            print("python scripts/train_model.py --cfg scripts/train_config_s.yaml --device 0")

if __name__ == "__main__":
    main() 