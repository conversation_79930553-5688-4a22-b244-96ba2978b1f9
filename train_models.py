import os
import sys
import subprocess
import time

def clear_screen():
    os.system('cls' if os.name == 'nt' else 'clear')

def print_banner():
    banner = """
==============================================================================
                  光伏电板热成像故障检测 - 模型训练
==============================================================================
    """
    print(banner)

def show_menu():
    print_banner()
    print("请选择要训练的模型类型：")
    print("1. YOLOv8n (较小模型，适合低配置GPU)")
    print("2. YOLOv8s (中等模型，需要更多GPU内存)")
    print("3. YOLOv8混合模型 (使用YOLOv8s的骨干网络和YOLOv8n的检测头)")
    print("4. 安装缺失的依赖")
    print("5. 清理训练缓存和进程")
    print("0. 退出")
    
    choice = input("\n请输入选项 [0-5]: ")
    return choice

def train_yolov8n():
    print_banner()
    print("正在训练YOLOv8n模型...")
    
    config_path = 'scripts/train_config.yaml'
    epochs = input("请输入训练轮数 [默认10]: ")
    batch = input("请输入批量大小 [默认4]: ")
    img_size = input("请输入图像尺寸 [默认384]: ")
    
    if epochs.strip():
        update_config_value(config_path, 'epochs', epochs)
    else:
        update_config_value(config_path, 'epochs', '10')
        
    if batch.strip():
        update_config_value(config_path, 'batch', batch)
    else:
        update_config_value(config_path, 'batch', '4')
        
    if img_size.strip():
        update_config_value(config_path, 'imgsz', img_size)
    else:
        update_config_value(config_path, 'imgsz', '384')
    
    start_time = time.time()
    subprocess.run(["python", "run_training.py"])
    
    end_time = time.time()
    duration = end_time - start_time
    hours, remainder = divmod(duration, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print(f"\nYOLOv8n训练完成，用时：{int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
    input("\n按Enter键返回主菜单...")

def train_yolov8s():
    print_banner()
    print("正在训练YOLOv8s模型...")
    
    config_path = 'scripts/train_config_s.yaml'
    epochs = input("请输入训练轮数 [默认30]: ")
    batch = input("请输入批量大小 [默认4]: ")
    img_size = input("请输入图像尺寸 [默认384]: ")
    
    if epochs.strip():
        update_config_value(config_path, 'epochs', epochs)
    else:
        update_config_value(config_path, 'epochs', '30')
        
    if batch.strip():
        update_config_value(config_path, 'batch', batch)
    else:
        update_config_value(config_path, 'batch', '4')
        
    if img_size.strip():
        update_config_value(config_path, 'imgsz', img_size)
    else:
        update_config_value(config_path, 'imgsz', '384')
    
    subprocess.run(["python", "train_yolov8s.py"])
    
    input("\n按Enter键返回主菜单...")

def train_hybrid_model():
    print_banner()
    print("正在创建并训练YOLOv8混合模型...")
    
    backbone_layers = input("请输入使用YOLOv8s的骨干网络层数 [默认8]: ")
    epochs = input("请输入训练轮数 [默认30]: ")
    batch = input("请输入批量大小 [默认4]: ")
    img_size = input("请输入图像尺寸 [默认384]: ")
    
    cmd = ["python", "train_hybrid_model.py"]
    
    if backbone_layers.strip():
        cmd.extend(["--backbone_layers", backbone_layers])
    
    if epochs.strip():
        cmd.extend(["--epochs", epochs])
        
    if batch.strip():
        cmd.extend(["--batch", batch])
        
    if img_size.strip():
        cmd.extend(["--imgsz", img_size])
    
    subprocess.run(cmd)
    
    input("\n按Enter键返回主菜单...")

def install_dependencies():
    print_banner()
    print("安装缺失的依赖...")
    
    # 基本依赖
    subprocess.run(["pip", "install", "ultralytics", "psutil", "torch", "torchvision", "matplotlib", "pyyaml"])
    
    # ONNX相关依赖（用于模型导出）
    subprocess.run(["pip", "install", "onnx", "onnxruntime-gpu", "onnxslim"])
    
    print("\n依赖安装完成！")
    input("\n按Enter键返回主菜单...")

def clean_cache():
    print_banner()
    print("清理训练缓存和进程...")
    
    # 终止所有Python进程
    if os.name == 'nt':  # Windows
        subprocess.run(["taskkill", "/F", "/IM", "python.exe"], stderr=subprocess.PIPE)
    else:  # Linux/Mac
        subprocess.run(["pkill", "-9", "python"], stderr=subprocess.PIPE)
    
    # 清理缓存文件
    cache_paths = [
        "data/pv_thermal_detection/train/labels.cache",
        "data/pv_thermal_detection/valid/labels.cache",
        "data/pv_thermal_detection/test/labels.cache"
    ]
    
    for path in cache_paths:
        if os.path.exists(path):
            os.remove(path)
            print(f"已删除缓存文件: {path}")
    
    print("\n清理完成！")
    input("\n按Enter键返回主菜单...")

def update_config_value(config_path, key, value):
    """更新配置文件中的特定值"""
    if not os.path.exists(config_path):
        print(f"配置文件不存在: {config_path}")
        return
    
    with open(config_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    with open(config_path, 'w', encoding='utf-8') as f:
        for line in lines:
            if line.startswith(f"{key}:"):
                f.write(f"{key}: {value}\n")
            else:
                f.write(line)

def main():
    while True:
        choice = show_menu()
        
        if choice == '0':
            print("退出程序...")
            sys.exit(0)
        elif choice == '1':
            train_yolov8n()
        elif choice == '2':
            train_yolov8s()
        elif choice == '3':
            train_hybrid_model()
        elif choice == '4':
            install_dependencies()
        elif choice == '5':
            clean_cache()
        else:
            print("无效选项，请重新选择")
            time.sleep(1)
        
        clear_screen()

if __name__ == "__main__":
    main() 