from ultralytics import YOLO
import cv2
import os
import glob

def test_model():
    # 加载模型
    model_path = "runs/detect/pv_thermal_detection2/weights/best.pt"
    print(f"加载模型: {model_path}")
    if not os.path.exists(model_path):
        print(f"错误：模型文件 {model_path} 不存在")
        return
    
    model = YOLO(model_path)
    
    # 获取测试图像
    test_dir = "data/pv_thermal_detection/test/images"
    
    # 列出所有测试图像
    image_files = glob.glob(os.path.join(test_dir, "*.jpg"))
    image_files += glob.glob(os.path.join(test_dir, "*.png"))
    
    if not image_files:
        print(f"错误：在 {test_dir} 中找不到图像")
        return
    
    print(f"找到 {len(image_files)} 个测试图像")
    
    # 创建结果目录
    output_dir = "test_results"
    os.makedirs(output_dir, exist_ok=True)
    
    # 测试5张图像
    for i, img_path in enumerate(image_files[:5]):
        print(f"\n处理图像 {i+1}: {os.path.basename(img_path)}")
        
        # 推理
        results = model.predict(img_path, conf=0.25, save=False)
        result = results[0]
        
        # 打印检测结果
        print(f"检测到 {len(result.boxes)} 个故障:")
        
        # 读取原始图像
        img = cv2.imread(img_path)
        
        # 在图像上绘制检测结果
        for j, box in enumerate(result.boxes):
            # 获取类别信息
            cls_id = int(box.cls[0].item())
            cls_name = result.names[cls_id]
            conf = box.conf[0].item()
            
            # 获取边界框坐标
            x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())
            
            # 绘制边界框和标签
            color = (0, 255, 0)  # 绿色
            cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)
            
            # 准备标签文本
            label = f"{cls_name} {conf:.2f}"
            print(f"  {j+1}. {label} 位置: [{x1}, {y1}, {x2}, {y2}]")
            
            # 添加标签
            cv2.putText(img, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
        
        # 保存结果图像
        output_path = os.path.join(output_dir, f"result_{os.path.basename(img_path)}")
        cv2.imwrite(output_path, img)
        print(f"结果图像已保存: {output_path}")
    
    print(f"\n所有结果已保存到 {output_dir} 目录")

if __name__ == "__main__":
    test_model() 