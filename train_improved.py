import os
import sys
import subprocess
import time
import torch
import psutil
import gc
import shutil
from pathlib import Path

# 清理内存和进程
def clean_environment():
    # 终止所有Python进程（除了当前进程）
    current_pid = os.getpid()
    for proc in psutil.process_iter(['pid', 'name']):
        if 'python' in proc.info['name'].lower() and proc.info['pid'] != current_pid:
            try:
                proc.kill()
                print(f"终止进程: {proc.info['pid']}")
            except:
                pass
    
    # 清理CUDA缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print("已清理CUDA缓存")
    
    # 清理系统缓存
    gc.collect()
    print("已清理系统缓存")
    
    # 删除缓存文件
    cache_files = [
        "data/pv_thermal_detection/train/labels.cache",
        "data/pv_thermal_detection/valid/labels.cache",
        "data/pv_thermal_detection/test/labels.cache"
    ]
    
    for file in cache_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"已删除缓存文件: {file}")

# 设置训练环境
def setup_environment():
    os.environ['CUDA_VISIBLE_DEVICES'] = '0'
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128,garbage_collection_threshold:0.6'
    
    # 检查CUDA是否可用
    if torch.cuda.is_available():
        print(f"CUDA可用: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / (1024**3):.1f} GB")
    else:
        print("CUDA不可用，将使用CPU训练（速度会非常慢）")

# 创建模型目录
def setup_directories():
    dirs = ["models", "runs/detect"]
    for d in dirs:
        os.makedirs(d, exist_ok=True)

# 运行优化训练
def run_improved_training():
    print("\n" + "="*80)
    print("启动优化训练...")
    print("使用增强的数据增强和优化的训练参数")
    print("="*80 + "\n")
    
    # 设置环境变量
    setup_environment()
    
    # 运行训练
    cmd = ["python", "scripts/start_training.py", "--cfg", "scripts/train_config_improved.yaml"]
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True)
        
        if result.returncode != 0:
            print(f"训练失败，返回代码: {result.returncode}")
            return False
        
        print("\n训练完成!")
        
        # 复制最佳模型到models目录以便于访问
        best_model_path = "runs/detect/pv_thermal_detection_improved/weights/best.pt"
        if os.path.exists(best_model_path):
            target_path = "models/pv_thermal_detection_best.pt"
            shutil.copy(best_model_path, target_path)
            print(f"已将最佳模型复制到: {target_path}")
            
            # 检查模型大小
            model_size_mb = os.path.getsize(target_path) / (1024 * 1024)
            print(f"模型大小: {model_size_mb:.2f} MB")
        
        return True
    
    except KeyboardInterrupt:
        print("\n训练被用户中断")
        return False
    except Exception as e:
        print(f"\n训练过程中发生错误: {e}")
        return False

# 主函数
def main():
    start_time = time.time()
    
    print("\n" + "="*80)
    print("光伏电板热成像故障检测 - 优化训练")
    print("="*80)
    
    # 清理环境
    print("\n清理环境...")
    clean_environment()
    
    # 设置目录
    setup_directories()
    
    # 运行训练
    success = run_improved_training()
    
    # 计算总用时
    end_time = time.time()
    duration = end_time - start_time
    hours, remainder = divmod(duration, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print("\n" + "="*80)
    print(f"训练{'成功' if success else '失败'}")
    print(f"总用时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
    print("="*80)
    
    if success:
        print("\n模型保存位置:")
        print("- 最佳模型: runs/detect/pv_thermal_detection_improved/weights/best.pt")
        print("- 复制的模型: models/pv_thermal_detection_best.pt")
        print("\n使用以下命令可以测试模型:")
        print("python -c \"from ultralytics import YOLO; model = YOLO('models/pv_thermal_detection_best.pt'); model.predict('data/pv_thermal_detection/test/images', save=True)\"")

if __name__ == "__main__":
    main() 