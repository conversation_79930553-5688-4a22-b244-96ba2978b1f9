import os
import sys
import yaml
import subprocess
from pathlib import Path
import shutil

def check_data_paths(data_yaml_path):
    """检查数据集路径是否正确，如果不正确则尝试修复"""
    print(f"检查数据集配置: {data_yaml_path}")
    
    if not os.path.exists(data_yaml_path):
        print(f"错误：找不到数据集配置文件 {data_yaml_path}")
        return False
    
    # 读取数据集配置
    with open(data_yaml_path, 'r', encoding='utf-8') as f:
        data_config = yaml.safe_load(f)
    
    # 检查训练、验证和测试数据集路径
    data_dir = os.path.dirname(data_yaml_path)
    train_path = os.path.join(data_dir, data_config.get('train', ''))
    valid_path = os.path.join(data_dir, data_config.get('val', ''))
    test_path = os.path.join(data_dir, data_config.get('test', ''))
    
    print(f"训练集路径: {train_path}")
    print(f"验证集路径: {valid_path}")
    print(f"测试集路径: {test_path}")
    
    # 检查训练集
    if 'images' in train_path and os.path.exists(train_path):
        print(f"训练集图像目录存在: {train_path}")
        train_img_count = len([f for f in os.listdir(train_path) if f.endswith(('.jpg', '.jpeg', '.png'))])
        print(f"训练集图像数量: {train_img_count}")
    else:
        print(f"警告：训练集图像目录不存在或路径格式不符合预期")
        
        # 尝试查找实际训练集图像目录
        possible_train_paths = [
            os.path.join(data_dir, 'train', 'images'),
            os.path.join(data_dir, 'train')
        ]
        
        for path in possible_train_paths:
            if os.path.exists(path) and any(f.endswith(('.jpg', '.jpeg', '.png')) for f in os.listdir(path)):
                print(f"找到可能的训练集目录: {path}")
                
                # 更新配置文件
                rel_path = os.path.relpath(path, data_dir)
                data_config['train'] = rel_path
                print(f"更新训练集路径为: {rel_path}")
                break
    
    # 检查验证集
    if 'images' in valid_path and os.path.exists(valid_path):
        print(f"验证集图像目录存在: {valid_path}")
        valid_img_count = len([f for f in os.listdir(valid_path) if f.endswith(('.jpg', '.jpeg', '.png'))])
        print(f"验证集图像数量: {valid_img_count}")
    else:
        print(f"警告：验证集图像目录不存在或路径格式不符合预期")
        
        # 尝试查找实际验证集图像目录
        possible_valid_paths = [
            os.path.join(data_dir, 'valid', 'images'),
            os.path.join(data_dir, 'valid'),
            os.path.join(data_dir, 'val', 'images'),
            os.path.join(data_dir, 'val')
        ]
        
        for path in possible_valid_paths:
            if os.path.exists(path) and any(f.endswith(('.jpg', '.jpeg', '.png')) for f in os.listdir(path)):
                print(f"找到可能的验证集目录: {path}")
                
                # 更新配置文件
                rel_path = os.path.relpath(path, data_dir)
                data_config['val'] = rel_path
                print(f"更新验证集路径为: {rel_path}")
                break
    
    # 检查测试集
    if 'images' in test_path and os.path.exists(test_path):
        print(f"测试集图像目录存在: {test_path}")
        test_img_count = len([f for f in os.listdir(test_path) if f.endswith(('.jpg', '.jpeg', '.png'))])
        print(f"测试集图像数量: {test_img_count}")
    else:
        print(f"警告：测试集图像目录不存在或路径格式不符合预期")
        
        # 尝试查找实际测试集图像目录
        possible_test_paths = [
            os.path.join(data_dir, 'test', 'images'),
            os.path.join(data_dir, 'test')
        ]
        
        for path in possible_test_paths:
            if os.path.exists(path) and any(f.endswith(('.jpg', '.jpeg', '.png')) for f in os.listdir(path)):
                print(f"找到可能的测试集目录: {path}")
                
                # 更新配置文件
                rel_path = os.path.relpath(path, data_dir)
                data_config['test'] = rel_path
                print(f"更新测试集路径为: {rel_path}")
                break
    
    # 备份原始配置文件
    shutil.copy(data_yaml_path, f"{data_yaml_path}.bak")
    print(f"已备份原始配置文件到 {data_yaml_path}.bak")
    
    # 保存更新后的配置文件
    with open(data_yaml_path, 'w', encoding='utf-8') as f:
        yaml.dump(data_config, f, default_flow_style=False)
    
    print(f"已更新数据集配置文件: {data_yaml_path}")
    return True

def check_labels(data_dir):
    """检查标签文件是否存在"""
    print("\n检查标签文件...")
    
    # 检查训练集标签
    train_images_dir = os.path.join(data_dir, "train", "images")
    train_labels_dir = os.path.join(data_dir, "train", "labels")
    
    if os.path.exists(train_images_dir) and os.path.exists(train_labels_dir):
        image_count = len([f for f in os.listdir(train_images_dir) if f.endswith(('.jpg', '.jpeg', '.png'))])
        label_count = len([f for f in os.listdir(train_labels_dir) if f.endswith('.txt')])
        print(f"训练集图像数量: {image_count}")
        print(f"训练集标签数量: {label_count}")
        
        if image_count != label_count:
            print(f"警告：训练集图像数量({image_count})与标签数量({label_count})不匹配")
    
    # 检查验证集标签
    valid_images_dir = os.path.join(data_dir, "valid", "images")
    valid_labels_dir = os.path.join(data_dir, "valid", "labels")
    
    if os.path.exists(valid_images_dir) and os.path.exists(valid_labels_dir):
        image_count = len([f for f in os.listdir(valid_images_dir) if f.endswith(('.jpg', '.jpeg', '.png'))])
        label_count = len([f for f in os.listdir(valid_labels_dir) if f.endswith('.txt')])
        print(f"验证集图像数量: {image_count}")
        print(f"验证集标签数量: {label_count}")
        
        if image_count != label_count:
            print(f"警告：验证集图像数量({image_count})与标签数量({label_count})不匹配")

def check_train_config(config_path):
    """检查训练配置文件"""
    print(f"\n检查训练配置: {config_path}")
    
    if not os.path.exists(config_path):
        print(f"错误：找不到训练配置文件 {config_path}")
        return False
    
    # 读取训练配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 检查关键参数
    data_path = config.get('data')
    if not data_path or not os.path.exists(data_path):
        print(f"警告：数据集路径不存在或未设置: {data_path}")
        
        # 尝试更新数据集路径
        if os.path.exists('data/pv_thermal_detection/data.yaml'):
            config['data'] = 'data/pv_thermal_detection/data.yaml'
            print(f"更新数据集路径为: data/pv_thermal_detection/data.yaml")
    
    # 检查批量大小，如果太大可能导致OOM
    batch_size = config.get('batch', 16)
    if batch_size > 16:
        print(f"警告：批量大小({batch_size})较大，可能导致内存不足。考虑减小到8-16")
    
    # 检查图像尺寸
    img_size = config.get('imgsz', 640)
    if img_size > 640:
        print(f"警告：图像尺寸({img_size})较大，可能增加训练时间和内存消耗")
    
    # 保存更新后的配置文件
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    print(f"训练配置检查完成")
    return True

def start_training():
    """启动模型训练流程"""
    # 确保UTF-8输出
    if sys.platform.startswith('win'):
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    
    print("="*80)
    print("光伏电板故障检测模型训练准备")
    print("="*80)
    
    # 数据集路径
    data_yaml_path = 'data/pv_thermal_detection/data.yaml'
    data_dir = 'data/pv_thermal_detection'
    train_config_path = 'scripts/train_config.yaml'
    
    # 检查数据集路径
    if not check_data_paths(data_yaml_path):
        return False
    
    # 检查标签文件
    check_labels(data_dir)
    
    # 检查训练配置
    if not check_train_config(train_config_path):
        return False
    
    # 读取训练配置以获取参数
    with open(train_config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    print("\n即将开始训练，参数如下:")
    print(f"- 模型: {config.get('model', 'yolov8n.pt')}")
    print(f"- 训练轮数: {config.get('epochs', 100)}")
    print(f"- 批量大小: {config.get('batch', 16)}")
    print(f"- 图像尺寸: {config.get('imgsz', 640)}")
    print(f"- 学习率: {config.get('lr0', 0.01)}")
    
    try:
        # 启动训练
        print("\n开始训练...")
        subprocess.run(["python", "scripts/run_pipeline.py"], check=True)
        print("训练完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"训练过程中出错: {e}")
        return False

if __name__ == "__main__":
    start_training() 