#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
YOLOv8训练结果分析脚本
用于分析YOLOv8模型的训练结果并生成性能报告
"""

import os
import sys
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import yaml
import shutil
import traceback
from pathlib import Path

# 确保可以导入YOLO
try:
    from ultralytics import YOLO
    import torch
except ImportError:
    print("错误: 未找到ultralytics或torch模块，请先安装: pip install ultralytics torch")
    sys.exit(1)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='分析YOLOv8训练结果')
    parser.add_argument('--results_dir', type=str, required=True, 
                        help='训练结果目录')
    parser.add_argument('--data_config', type=str, default='data/pv_thermal_detection/data.yaml',
                        help='数据配置文件')
    parser.add_argument('--output_dir', type=str, default=None,
                        help='分析结果输出目录，默认为结果目录下的analysis子目录')
    parser.add_argument('--csv', type=str, default=None,
                        help='训练结果CSV文件路径，默认为results_dir/results.csv')
    parser.add_argument('--conf_threshold', type=float, default=0.25,
                        help='置信度阈值')
    parser.add_argument('--iou_threshold', type=float, default=0.45,
                        help='IoU阈值')
    parser.add_argument('--max_samples', type=int, default=10,
                        help='每个类别的最大样本数')
    parser.add_argument('--cpu_only', action='store_true',
                        help='仅使用CPU分析')
    
    args = parser.parse_args()
    
    # 设置默认输出目录
    if args.output_dir is None:
        args.output_dir = os.path.join(args.results_dir)
    
    return args

def plot_training_curves(results_csv, output_dir):
    """绘制训练曲线"""
    if not os.path.exists(results_csv):
        print(f"错误: 结果CSV文件不存在: {results_csv}")
        return False
    
    try:
        # 读取训练结果
        results = pd.read_csv(results_csv)
        
        # 准备图表
        fig, axs = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 损失曲线
        ax = axs[0, 0]
        ax.plot(results['epoch'], results['train/box_loss'], label='train/box_loss')
        ax.plot(results['epoch'], results['train/cls_loss'], label='train/cls_loss')
        ax.plot(results['epoch'], results['train/dfl_loss'], label='train/dfl_loss')
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Loss')
        ax.set_title('Training Losses')
        ax.legend()
        ax.grid(True)
        
        # 2. mAP曲线
        ax = axs[0, 1]
        ax.plot(results['epoch'], results['metrics/mAP50(B)'], label='mAP50(B)')
        ax.plot(results['epoch'], results['metrics/mAP50-95(B)'], label='mAP50-95(B)')
        ax.set_xlabel('Epoch')
        ax.set_ylabel('mAP')
        ax.set_title('Mean Average Precision')
        ax.legend()
        ax.grid(True)
        
        # 3. Precision/Recall曲线
        ax = axs[1, 0]
        ax.plot(results['epoch'], results['metrics/precision(B)'], label='Precision(B)')
        ax.plot(results['epoch'], results['metrics/recall(B)'], label='Recall(B)')
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Value')
        ax.set_title('Precision & Recall')
        ax.legend()
        ax.grid(True)
        
        # 4. 学习率曲线
        ax = axs[1, 1]
        if 'lr/pg0' in results.columns:
            ax.plot(results['epoch'], results['lr/pg0'], label='Learning Rate')
            ax.set_xlabel('Epoch')
            ax.set_ylabel('Learning Rate')
            ax.set_title('Learning Rate Schedule')
            ax.grid(True)
        
        plt.tight_layout()
        output_path = os.path.join(output_dir, 'training_curves.png')
        plt.savefig(output_path)
        plt.close()
        
        print(f"训练曲线已保存到: {output_path}")
        return True
    except Exception as e:
        print(f"绘制训练曲线时出错: {e}")
        traceback.print_exc()
        return False

def analyze_model_performance(model_path, data_yaml, output_dir, conf_thresh=0.25, iou_thresh=0.45, cpu_only=False, batch_size=1, num_threads=1):
    """分析模型性能"""
    if not os.path.exists(model_path):
        print(f"错误: 模型文件不存在: {model_path}")
        return False
    
    try:
        # 控制CUDA线程数量和内存使用
        if torch.cuda.is_available() and not cpu_only:
            print(f"使用GPU进行模型分析，线程数: {num_threads}")
            torch.set_num_threads(num_threads)
            # 限制可用CUDA设备
            os.environ["CUDA_VISIBLE_DEVICES"] = "0"
            # 启用内存自动释放
            torch.cuda.empty_cache()
        else:
            print("使用CPU进行模型分析")
            os.environ["CUDA_VISIBLE_DEVICES"] = ""
        
        # 加载模型
        model = YOLO(model_path)
        
        # 加载数据集配置
        with open(data_yaml, 'r') as f:
            data_config = yaml.safe_load(f)
        
        class_names = data_config.get('names', [])
        
        # 运行验证
        print(f"\n使用置信度阈值 {conf_thresh} 和 IoU阈值 {iou_thresh} 评估模型...")
        val_results = model.val(data=data_yaml, conf=conf_thresh, iou=iou_thresh)
        
        # 打印总体性能
        print("\n总体性能:")
        print(f"mAP@0.5: {val_results.box.map50:.4f}")
        print(f"mAP@0.5:0.95: {val_results.box.map:.4f}")
        print(f"精确率 (Precision): {val_results.box.mp:.4f}")
        print(f"召回率 (Recall): {val_results.box.mr:.4f}")
        
        # 创建性能报告文件
        report_path = os.path.join(output_dir, 'model_performance.txt')
        with open(report_path, 'w') as f:
            f.write("# YOLOv8s 光伏异常检测模型性能评估\n\n")
            
            f.write("## 总体性能\n")
            f.write(f"mAP@0.5: {val_results.box.map50:.4f}\n")
            f.write(f"mAP@0.5:0.95: {val_results.box.map:.4f}\n")
            f.write(f"精确率 (Precision): {val_results.box.mp:.4f}\n")
            f.write(f"召回率 (Recall): {val_results.box.mr:.4f}\n\n")
            
            f.write("## 各类别性能\n")
            # 使用安全属性访问
            try:
                class_maps = getattr(val_results.box, 'maps50', [])
                if not class_maps or len(class_maps) != len(class_names):
                    class_maps = [val_results.box.map50] * len(class_names)
            except Exception as e:
                print(f"警告: 无法读取类别性能，使用总体性能代替: {e}")
                class_maps = [val_results.box.map50] * len(class_names)

            for i, name in enumerate(class_names):
                f.write(f"\n类别 {i}: {name}\n")
                f.write(f"mAP@0.5: {getattr(val_results.box, 'maps50', [0]*len(class_names))[i] if hasattr(val_results.box, 'maps50') else val_results.box.map50:.4f}\n")
                f.write(f"精确率: {val_results.box.mps[i]:.4f}\n")
                f.write(f"召回率: {val_results.box.mrs[i]:.4f}\n")
        
        print(f"性能报告已保存至: {report_path}")
        
        # 创建性能可视化图表
        plt.figure(figsize=(12, 6))
        
        # 绘制每个类别的mAP, Precision, Recall
        x = np.arange(len(class_names))
        width = 0.25
        
        plt.bar(x - width, val_results.box.maps50, width, label='mAP@0.5')
        plt.bar(x, val_results.box.mps, width, label='Precision')
        plt.bar(x + width, val_results.box.mrs, width, label='Recall')
        
        plt.xlabel('类别')
        plt.ylabel('性能')
        plt.title('各类别检测性能')
        plt.xticks(x, class_names, rotation=45, ha='right')
        plt.ylim(0, 1.0)
        plt.legend()
        plt.tight_layout()
        
        # 保存图表
        output_path = os.path.join(output_dir, 'class_performance.png')
        plt.savefig(output_path)
        plt.close()
        
        print(f"类别性能可视化已保存到 {output_path}")
        return val_results
    except Exception as e:
        print(f"分析模型性能时出错: {e}")
        traceback.print_exc()
        return None
    finally:
        # 清理内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

def analyze_training_results(results_csv, output_dir):
    """分析训练结果并生成摘要报告"""
    if not os.path.exists(results_csv):
        print(f"错误: 结果文件不存在: {results_csv}")
        return False
    
    try:
        # 读取CSV结果文件
        results = pd.read_csv(results_csv)
        
        # 输出文件路径
        os.makedirs(output_dir, exist_ok=True)
        output_txt = os.path.join(output_dir, 'training_summary.txt')
        
        print(f"正在分析训练结果: {results_csv}")
        print(f"总共训练了 {len(results)} 个epoch")
        
        # 获取关键指标
        final_epoch = results.iloc[-1]
        best_map = results['metrics/mAP50(B)'].max()
        best_map_epoch = results['metrics/mAP50(B)'].idxmax() + 1  # +1因为epoch从1开始
        
        best_map50_95 = results['metrics/mAP50-95(B)'].max()
        best_map50_95_epoch = results['metrics/mAP50-95(B)'].idxmax() + 1
        
        # 创建摘要报告
        with open(output_txt, 'w') as f:
            f.write("# 光伏异常检测YOLOv8s模型训练结果\n\n")
            
            f.write("## 训练摘要\n")
            f.write(f"总训练轮次: {len(results)}\n")
            f.write(f"最佳mAP50: {best_map:.4f} (第{best_map_epoch}轮)\n")
            f.write(f"最佳mAP50-95: {best_map50_95:.4f} (第{best_map50_95_epoch}轮)\n\n")
            
            f.write("## 最终轮次性能\n")
            f.write(f"mAP50: {final_epoch['metrics/mAP50(B)']:.4f}\n")
            f.write(f"mAP50-95: {final_epoch['metrics/mAP50-95(B)']:.4f}\n")
            f.write(f"精度(Precision): {final_epoch['metrics/precision(B)']:.4f}\n")
            f.write(f"召回率(Recall): {final_epoch['metrics/recall(B)']:.4f}\n\n")
            
            f.write("## 每10个轮次的性能\n")
            f.write("轮次\tmAP50\tmAP50-95\t精度\t召回率\n")
            for i in range(0, len(results), 10):
                if i < len(results):
                    epoch = results.iloc[i]
                    f.write(f"{i+1}\t{epoch['metrics/mAP50(B)']:.4f}\t{epoch['metrics/mAP50-95(B)']:.4f}\t{epoch['metrics/precision(B)']:.4f}\t{epoch['metrics/recall(B)']:.4f}\n")
        
        print(f"训练分析摘要已保存至: {output_txt}")
        return True
    except Exception as e:
        print(f"分析结果时出错: {e}")
        traceback.print_exc()
        return False

def copy_visualization_files(source_dir, target_dir):
    """复制训练生成的可视化文件到目标目录"""
    try:
        source_path = Path(source_dir)
        os.makedirs(target_dir, exist_ok=True)
        
        # 查找可视化图像
        visualization_files = [
            source_path / 'labels.jpg',
            source_path / 'labels_correlogram.jpg',
            source_path / 'train_batch0.jpg',
            source_path / 'train_batch1.jpg',
            source_path / 'train_batch2.jpg'
        ]
        
        copied_count = 0
        for file_path in visualization_files:
            if file_path.exists():
                target_path = os.path.join(target_dir, file_path.name)
                shutil.copy(file_path, target_path)
                print(f"复制 {file_path} -> {target_path}")
                copied_count += 1
        
        # 查找结果文件
        results_files = list(source_path.glob('results.*'))
        for file_path in results_files:
            target_path = os.path.join(target_dir, file_path.name)
            shutil.copy(file_path, target_path)
            print(f"复制 {file_path} -> {target_path}")
            copied_count += 1
        
        # 复制混淆矩阵等文件
        confusion_files = list(source_path.glob('confusion_matrix*'))
        for file_path in confusion_files:
            target_path = os.path.join(target_dir, file_path.name)
            shutil.copy(file_path, target_path)
            print(f"复制 {file_path} -> {target_path}")
            copied_count += 1
        
        # 复制PR曲线等文件
        pr_files = list(source_path.glob('PR_curve*'))
        for file_path in pr_files:
            target_path = os.path.join(target_dir, file_path.name)
            shutil.copy(file_path, target_path)
            print(f"复制 {file_path} -> {target_path}")
            copied_count += 1
        
        # 复制F1曲线等文件
        f1_files = list(source_path.glob('F1_curve*'))
        for file_path in f1_files:
            target_path = os.path.join(target_dir, file_path.name)
            shutil.copy(file_path, target_path)
            print(f"复制 {file_path} -> {target_path}")
            copied_count += 1
        
        if copied_count > 0:
            print(f"已复制 {copied_count} 个可视化文件到 {target_dir}")
        else:
            print(f"未找到可复制的可视化文件")
        
        return copied_count > 0
    except Exception as e:
        print(f"复制可视化文件时出错: {e}")
        traceback.print_exc()
        return False

def main():
    # 解析命令行参数
    args = parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    print(f"分析结果将保存到: {args.output_dir}")
    
    # 确定CSV结果文件路径
    if args.csv:
        results_csv = args.csv
    else:
        # 尝试从results目录查找
        results_csv = os.path.join(args.results_dir, 'results.csv')
    
    if not os.path.exists(results_csv):
        print(f"未找到训练结果CSV文件: {results_csv}")
        results_csv = None
    else:
        print(f"使用训练结果文件: {results_csv}")
        
        # 绘制训练曲线
        plot_training_curves(results_csv, args.output_dir)
        
        # 分析训练结果
        analyze_training_results(results_csv, args.output_dir)
    
    # 确定最佳模型路径
    best_model_path = os.path.join(args.results_dir, 'weights/best.pt')
    if not os.path.exists(best_model_path):
        print(f"未找到最佳模型: {best_model_path}")
        best_model_path = None
    else:
        print(f"使用最佳模型: {best_model_path}")
        
        # 分析模型性能
        analyze_model_performance(
            best_model_path, 
            args.data_config, 
            args.output_dir, 
            conf_thresh=args.conf_threshold, 
            iou_thresh=args.iou_threshold,
            cpu_only=args.cpu_only
        )
    
    # 复制可视化文件
    copy_visualization_files(args.results_dir, args.output_dir)
    
    print("\n分析完成！结果已保存到:", args.output_dir)

if __name__ == "__main__":
    main() 