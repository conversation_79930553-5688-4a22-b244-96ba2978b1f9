import os
import argparse
import subprocess
import time
from pathlib import Path
import sys

def parse_args():
    parser = argparse.ArgumentParser(description='运行完整的光伏电板故障检测训练和评估流程')
    parser.add_argument('--config', type=str, default='scripts/train_config.yaml',
                        help='训练配置文件路径')
    parser.add_argument('--epochs', type=int, default=None, help='训练轮数，覆盖配置文件设置')
    parser.add_argument('--batch', type=int, default=None, help='批量大小，覆盖配置文件设置')
    parser.add_argument('--imgsz', type=int, default=None, help='图像尺寸，覆盖配置文件设置')
    parser.add_argument('--device', type=str, default=None, help='训练设备，例如 0 或 cpu')
    parser.add_argument('--results-dir', type=str, default='runs/detect/pv_thermal_detection',
                        help='结果保存目录')
    parser.add_argument('--skip-train', action='store_true', help='跳过训练，直接进行评估')
    parser.add_argument('--skip-analyze', action='store_true', help='跳过结果分析')
    parser.add_argument('--conf-thresh', type=float, default=0.25, help='评估时的置信度阈值')
    parser.add_argument('--max-samples', type=int, default=10, help='可视化时每个类别的最大样本数')
    return parser.parse_args()

def run_command(cmd, desc):
    """运行命令并打印状态"""
    print(f"\n{'='*80}")
    print(f"开始 {desc}...")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*80}\n")
    
    start_time = time.time()
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                              encoding="utf-8", errors="replace", bufsize=1)
    
    # 实时打印输出
    for line in iter(process.stdout.readline, ''):
        print(line, end='')
    
    process.stdout.close()
    return_code = process.wait()
    
    elapsed_time = time.time() - start_time
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print(f"\n{'='*80}")
    print(f"{desc} {'成功' if return_code == 0 else '失败'}")
    print(f"用时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
    print(f"{'='*80}\n")
    
    return return_code == 0

def main():
    # 确保UTF-8输出
    if sys.platform.startswith('win'):
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    
    # 解析命令行参数
    args = parse_args()
    
    # 检查必要的目录
    os.makedirs("data", exist_ok=True)
    os.makedirs("models", exist_ok=True)
    
    # 确保数据集已准备好
    if not os.path.exists("data/pv_thermal_detection/data.yaml"):
        print("错误: 未找到数据集配置文件 data/pv_thermal_detection/data.yaml")
        print("请确保数据集已正确放置在 data/pv_thermal_detection 目录中")
        return False
    
    # 成功标志
    success = True
    
    # 1. 训练模型
    if not args.skip_train:
        # 构建训练命令
        train_cmd = ["python", "scripts/train_model.py", "--cfg", args.config]
        
        # 添加可选参数
        if args.epochs is not None:
            train_cmd.extend(["--epochs", str(args.epochs)])
        if args.batch is not None:
            train_cmd.extend(["--batch", str(args.batch)])
        if args.imgsz is not None:
            train_cmd.extend(["--imgsz", str(args.imgsz)])
        if args.device is not None:
            train_cmd.extend(["--device", str(args.device)])
        
        # 运行训练
        success = run_command(train_cmd, "模型训练")
        if not success:
            print("训练失败，流程中止")
            return False
    else:
        print("跳过训练步骤")
    
    # 2. 分析结果
    if not args.skip_analyze and success:
        # 构建分析命令
        analyze_cmd = [
            "python", "scripts/analyze_results.py",
            "--results", args.results_dir,
            "--data", "data/pv_thermal_detection/data.yaml",
            "--conf", str(args.conf_thresh),
            "--max-samples", str(args.max_samples)
        ]
        
        # 运行分析
        success = run_command(analyze_cmd, "结果分析")
        if not success:
            print("结果分析失败")
    elif args.skip_analyze:
        print("跳过结果分析步骤")
    
    # 3. 导出模型到树莓派格式
    if success:
        # 构建导出命令
        deploy_cmd = ["python", "scripts/deploy_to_raspi.py"]
        
        # 运行导出
        success = run_command(deploy_cmd, "树莓派模型导出")
        if not success:
            print("模型导出失败")
    
    # 打印总结
    print("\n" + "="*80)
    if success:
        print("光伏电板故障检测模型训练和评估流程已成功完成!")
        
        # 打印重要文件位置
        results_dir = Path(args.results_dir)
        model_path = results_dir / "weights" / "best.pt"
        onnx_path = results_dir / "weights" / "best.onnx"
        raspi_dir = Path("raspberry_pi_model")
        
        print("\n关键文件:")
        if model_path.exists():
            print(f"- 最佳模型权重: {model_path}")
        if onnx_path.exists():
            print(f"- ONNX模型: {onnx_path}")
        if raspi_dir.exists():
            print(f"- 树莓派部署包: {raspi_dir}")
            
        print("\n分析结果:")
        print(f"- 训练曲线: {results_dir}/training_analysis.png")
        print(f"- 类别性能: {results_dir}/class_performance.png")
        print(f"- 混淆矩阵: {results_dir}/confusion_matrix.png")
        print(f"- 预测可视化: {results_dir}/predictions/")
    else:
        print("光伏电板故障检测模型训练和评估流程未完全成功，请查看错误信息")
    
    print("="*80 + "\n")
    return success

if __name__ == "__main__":
    main() 