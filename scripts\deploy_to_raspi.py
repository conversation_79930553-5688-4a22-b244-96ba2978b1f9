from ultralytics import YOLO
import os
import glob
import shutil
import yaml

def export_for_raspberry_pi(model_path, export_dir="raspberry_pi_model"):
    """
    导出模型为适合在树莓派上运行的格式
    
    参数:
        model_path: 模型文件路径
        export_dir: 导出目录
    """
    print(f"加载模型: {model_path}")
    model = YOLO(model_path)
    
    # 创建导出目录
    os.makedirs(export_dir, exist_ok=True)
    
    # 导出为ONNX格式 (适合OpenCV DNN和ONNX Runtime)
    print("导出模型为ONNX格式...")
    exported_model = model.export(format="onnx", dynamic=True, simplify=True)
    
    # 复制导出的模型到导出目录
    onnx_filename = os.path.basename(exported_model)
    shutil.copy(exported_model, os.path.join(export_dir, onnx_filename))
    
    # 为树莓派准备推理脚本
    inference_script = os.path.join(export_dir, "detect.py")
    print(f"创建树莓派推理脚本: {inference_script}")
    
    # 获取数据集类别信息
    data_yaml_path = "data/pv_thermal_detection/data.yaml"
    classes = []
    if os.path.exists(data_yaml_path):
        with open(data_yaml_path, 'r') as f:
            data_config = yaml.safe_load(f)
            classes = data_config.get('names', [])
    
    # 创建树莓派推理脚本
    with open(inference_script, 'w') as f:
        f.write("""import cv2
import numpy as np
import time
import argparse

# 检测类别名称
CLASSES = {classes}

def preprocess(img, input_size):
    # 调整图像大小并进行归一化
    img = cv2.resize(img, (input_size, input_size))
    img = img.astype(np.float32) / 255.0
    # 转换为NCHW (批次大小, 通道数, 高度, 宽度)
    blob = np.transpose(img, (2, 0, 1))
    blob = np.expand_dims(blob, axis=0)
    return blob

def postprocess(outputs, img_shape, conf_threshold=0.25, iou_threshold=0.45):
    # 从ONNX模型输出中提取检测框
    predictions = np.squeeze(outputs[0])
    
    # 过滤低置信度的检测结果
    mask = predictions[:, 4] >= conf_threshold
    predictions = predictions[mask]
    
    if len(predictions) == 0:
        return []
    
    # 执行非最大抑制 (NMS)
    boxes = []
    for pred in predictions:
        # 获取坐标、置信度和类别
        x, y, w, h = pred[0:4]
        confidence = pred[4]
        class_id = np.argmax(pred[5:])
        class_score = pred[5 + class_id]
        
        # 根据原始图像尺寸调整坐标
        orig_h, orig_w = img_shape[:2]
        x1 = int((x - w/2) * orig_w)
        y1 = int((y - h/2) * orig_h)
        x2 = int((x + w/2) * orig_w)
        y2 = int((y + h/2) * orig_h)
        
        boxes.append([x1, y1, x2, y2, confidence * class_score, class_id])
    
    # 执行NMS
    indices = cv2.dnn.NMSBoxes(
        [box[:4] for box in boxes],
        [box[4] for box in boxes],
        conf_threshold,
        iou_threshold
    )
    
    result = []
    for i in indices:
        box = boxes[i]
        result.append({
            'box': box[:4],  # [x1, y1, x2, y2]
            'score': float(box[4]),
            'class_id': int(box[5]),
            'class_name': CLASSES[int(box[5])] if int(box[5]) < len(CLASSES) else f"类别{int(box[5])}"
        })
    
    return result

def draw_detections(img, detections):
    # 在图像上绘制检测框和标签
    for det in detections:
        x1, y1, x2, y2 = det['box']
        score = det['score']
        class_id = det['class_id']
        class_name = det['class_name']
        
        # 为不同类别设置不同颜色
        color = (0, 255, 0)  # 默认绿色
        if class_id % 3 == 0:
            color = (0, 0, 255)  # 红色
        elif class_id % 3 == 1:
            color = (255, 0, 0)  # 蓝色
        
        # 绘制框
        cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)
        
        # 绘制标签
        label = f"{class_name}: {score:.2f}"
        (label_width, label_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
        cv2.rectangle(img, (x1, y1-label_height-5), (x1+label_width, y1), color, -1)
        cv2.putText(img, label, (x1, y1-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    return img

def main():
    parser = argparse.ArgumentParser(description='PV热敏检测部署演示')
    parser.add_argument('--model', default='{onnx_filename}', help='ONNX模型路径')
    parser.add_argument('--input_size', type=int, default=640, help='输入尺寸')
    parser.add_argument('--camera', type=int, default=0, help='摄像头索引')
    parser.add_argument('--conf_threshold', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--iou_threshold', type=float, default=0.45, help='IOU阈值')
    args = parser.parse_args()

    # 加载ONNX模型
    print(f"加载模型: {args.model}")
    net = cv2.dnn.readNetFromONNX(args.model)

    # 捕获摄像头视频流
    print(f"启动摄像头...")
    cap = cv2.VideoCapture(args.camera)
    if not cap.isOpened():
        print("错误: 无法打开摄像头!")
        return

    print("按 'q' 键退出")
    while True:
        # 读取帧
        ret, frame = cap.read()
        if not ret:
            print("无法从摄像头读取帧!")
            break

        # 预处理帧
        start_time = time.time()
        blob = preprocess(frame, args.input_size)

        # 推理
        net.setInput(blob)
        outputs = net.forward(net.getUnconnectedOutLayersNames())

        # 后处理结果
        detections = postprocess(outputs, frame.shape, args.conf_threshold, args.iou_threshold)

        # 计算FPS
        fps = 1.0 / (time.time() - start_time)

        # 绘制检测结果
        frame = draw_detections(frame, detections)

        # 显示FPS
        cv2.putText(frame, f"FPS: {fps:.1f}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

        # 显示结果
        cv2.imshow('PV热敏检测', frame)

        # 按 'q' 键退出
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    # 释放资源
    cap.release()
    cv2.destroyAllWindows()

if __name__ == '__main__':
    main()
""".format(
    classes=str(classes),
    onnx_filename=onnx_filename
))
    
    # 创建一个README文件，说明如何在树莓派上使用
    readme_path = os.path.join(export_dir, "README.md")
    print(f"创建说明文档: {readme_path}")
    
    with open(readme_path, 'w') as f:
        f.write("""# 光伏电板故障检测 - 树莓派部署

本目录包含为树莓派准备的光伏电板故障检测模型和脚本。

## 安装依赖

在树莓派上安装必要的依赖：

```bash
# 安装OpenCV
sudo apt update
sudo apt install -y python3-opencv

# 安装其他依赖
pip3 install numpy pyyaml
```

## 运行检测

使用摄像头实时检测：

```bash
python3 detect.py
```

### 可选参数

- `--model`: ONNX模型路径（默认: '{onnx_filename}'）
- `--input_size`: 输入图像尺寸（默认: 640）
- `--camera`: 摄像头索引（默认: 0）
- `--conf_threshold`: 置信度阈值（默认: 0.25）
- `--iou_threshold`: IOU阈值（默认: 0.45）

示例：

```bash
python3 detect.py --conf_threshold 0.5 --camera 1
```

## 性能优化

如果在树莓派上运行速度较慢，可以尝试：

1. 减小输入尺寸（例如：`--input_size 320`）
2. 使用树莓派的GPU加速（需安装ONNX Runtime）：

```bash
pip3 install onnxruntime
```

然后修改`detect.py`脚本以使用ONNX Runtime代替OpenCV DNN。
""".format(onnx_filename=onnx_filename))
    
    print(f"\n模型和脚本已准备完毕，保存在 {export_dir} 目录下")
    print(f"请将此目录复制到树莓派，并按照 {readme_path} 中的说明进行部署")


if __name__ == "__main__":
    # 查找训练好的模型
    model_path = "runs/detect/pv_thermal_detection/weights/best.pt"
    
    # 不存在时使用备选路径
    if not os.path.exists(model_path):
        # 检查是否有其他权重文件
        weight_files = glob.glob("runs/detect/pv_thermal_detection/weights/*.pt")
        if weight_files:
            model_path = weight_files[0]
            print(f"找不到best.pt，使用替代模型: {model_path}")
        else:
            # 最后尝试使用导出的ONNX模型
            model_path = "runs/detect/pv_thermal_detection/weights/best.onnx"
            if not os.path.exists(model_path):
                print("错误：找不到训练好的模型文件。请先运行 train_model.py 训练模型。")
                exit(1)
    
    # 导出模型
    export_for_raspberry_pi(model_path) 